# BitNet Multi-Agent System - Quick Start Guide

## Prerequisites

- Python 3.9 or higher
- Git
- CMake
- Clang 18+
- Docker (optional but recommended)
- 16GB+ RAM recommended
- vLLM server running (for reasoning model)

## Installation

### 1. <PERSON><PERSON> and Setup

```bash
# Clone the repository (if not already done)
cd 1-bit

# Run the automated setup
python setup_bitnet_system.py
```

The setup script will:
- Install Python dependencies
- Clone BitNet repository
- Download the BitNet model (2.4GB)
- Create necessary directories
- Build Docker images (if Docker available)

### 2. Configure Environment

```bash
# Copy environment template
cp .env.example .env

# Edit configuration (important!)
nano .env
```

**Key settings to configure:**

```env
# Number of BitNet agents (adjust based on your CPU cores)
BITNET_AGENT_COUNT=4

# Path to BitNet model (auto-configured by setup)
BITNET_MODEL_PATH=./models/BitNet-b1.58-2B-4T

# Your vLLM reasoning model endpoint
REASONING_MODEL_URL=http://localhost:8001/v1
REASONING_MODEL_NAME=llama-3.1-8b-instruct
REASONING_MODEL_API_KEY=your-api-key-here

# Enable Docker deployment
DOCKER_ENABLED=true
```

### 3. Start Your vLLM Reasoning Model

The system requires a reasoning model running via vLLM. Start your existing vLLM server:

```bash
# Example vLLM startup (adjust for your setup)
vllm serve llama-3.1-8b-instruct \
    --host 0.0.0.0 \
    --port 8001 \
    --max-model-len 8196
```

## Quick Start Options

### Option A: Docker Deployment (Recommended)

```bash
# Start the entire system with Docker
python run_multi_agent_system.py --interactive
```

This will:
- Start Redis container
- Launch 4 BitNet agent containers
- Start the orchestrator
- Enter interactive mode

### Option B: Local Development

```bash
# Start without Docker (for development)
python run_multi_agent_system.py --no-docker --interactive
```

This will:
- Start local Redis (if available)
- Launch BitNet agents as local processes
- Start the orchestrator
- Enter interactive mode

## Testing the System

### 1. Run System Tests

```bash
# Verify everything is working
python test_system.py
```

### 2. Interactive Mode

Once the system is running, you can interact with it:

```
bitnet> help
Available commands:
  help                 - Show this help message
  status              - Show system status
  task <description>  - Run a task with the given description
  quit/exit           - Stop the system and exit

bitnet> status
System Status:
- Active Agents: 4
- Reasoning Agent: healthy
- Total Tasks: 0

bitnet> task Explain how quicksort works and implement it in Python

Processing task: Explain how quicksort works and implement it in Python
Task completed (Confidence: 0.85)
Result:
Quicksort is a divide-and-conquer sorting algorithm...
[Full response with code implementation]
```

## Example Tasks

Try these example tasks to test different capabilities:

### Code Generation
```
task Generate a Python function to calculate the Fibonacci sequence with memoization
```

### Analysis
```
task Compare the time complexity of bubble sort vs merge sort
```

### Code Review
```
task Review this Python code for potential security vulnerabilities: [paste code]
```

### Research
```
task Research the latest developments in transformer architecture optimization
```

## System Monitoring

### Check System Status

```bash
# Via API
curl http://localhost:9200/status

# Via interactive mode
bitnet> status
```

### View Logs

```bash
# Docker logs
docker-compose -f docker/docker-compose.yml logs -f

# Local logs
tail -f logs/system.log
tail -f logs/orchestrator.log
tail -f logs/bitnet_agent_*.log
```

### Monitor Resources

```bash
# Docker resource usage
docker stats

# System resources
htop
```

## Configuration Tuning

### Performance Optimization

```env
# Increase agents for more parallelism (requires more CPU/RAM)
BITNET_AGENT_COUNT=8

# Adjust context length based on your needs
MAX_CONTEXT_LENGTH=16384

# Enable/disable parallel processing
PARALLEL_PROCESSING=true

# Adjust BitNet inference parameters
BITNET_THREADS=4
BITNET_TEMPERATURE=0.7
```

### Memory Management

```env
# Reduce memory usage
BITNET_MAX_TOKENS=2048
CACHE_SIZE=500

# Increase for better performance (if you have RAM)
BITNET_MAX_TOKENS=4096
CACHE_SIZE=2000
```

## Troubleshooting

### Common Issues

1. **"No BitNet model found"**
   ```bash
   # Re-download the model
   huggingface-cli download microsoft/BitNet-b1.58-2B-4T-gguf --local-dir models/BitNet-b1.58-2B-4T
   ```

2. **"Redis connection failed"**
   ```bash
   # Start Redis manually
   redis-server --daemonize yes
   
   # Or use Docker
   docker run -d -p 6379:6379 redis:7-alpine
   ```

3. **"Reasoning agent not healthy"**
   - Check if your vLLM server is running
   - Verify the REASONING_MODEL_URL in .env
   - Test the endpoint: `curl http://localhost:8001/v1/models`

4. **"Docker build failed"**
   ```bash
   # Build manually
   cd docker
   docker-compose build --no-cache
   ```

### Debug Mode

```bash
# Enable debug logging
export LOG_LEVEL=DEBUG
export ENABLE_DETAILED_LOGGING=true

# Run with verbose output
python run_multi_agent_system.py --no-docker --interactive
```

### Performance Issues

1. **Slow responses**: Increase BITNET_THREADS or reduce BITNET_AGENT_COUNT
2. **High memory usage**: Reduce BITNET_MAX_TOKENS or CACHE_SIZE
3. **Agent timeouts**: Increase AGENT_COMMUNICATION_TIMEOUT

## Scaling the System

### Horizontal Scaling (More Agents)

```env
# Increase agent count
BITNET_AGENT_COUNT=8

# Add more specialized roles
BITNET_AGENT_ROLES=analyzer,coder,reviewer,researcher,optimizer,tester,documenter,debugger
```

### Vertical Scaling (More Resources)

```yaml
# In docker-compose.yml, increase resource limits
deploy:
  resources:
    limits:
      cpus: '4'
      memory: 8G
```

## Next Steps

1. **Integrate with your workflow**: Use the API endpoints to integrate with your applications
2. **Customize agents**: Modify agent roles and prompts for your specific use cases
3. **Add monitoring**: Set up Prometheus/Grafana for production monitoring
4. **Scale up**: Deploy on multiple machines for larger workloads

## Getting Help

- Check the logs: `logs/system.log`
- Run tests: `python test_system.py`
- Review configuration: `cat .env`
- Check system status: API endpoint `/status`

For more detailed information, see `SYSTEM_ARCHITECTURE.md`.
