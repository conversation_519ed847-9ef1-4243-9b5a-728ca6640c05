#!/usr/bin/env python3
"""Test script to discover vLLM API endpoints and format."""

import asyncio
import aiohttp
import json
import os
from dotenv import load_dotenv

load_dotenv()

async def test_vllm_api():
    """Test different vLLM API endpoints and formats."""
    vllm_url = os.getenv('REASONING_MODEL_URL', 'http://localhost:8001/v1')
    model_name = os.getenv('REASONING_MODEL_NAME', 'llama-3.1-8b-instruct')
    
    print(f"🔧 Testing vLLM API at: {vllm_url}")
    print(f"🤖 Model name: {model_name}")
    print("=" * 60)
    
    async with aiohttp.ClientSession() as session:
        # Test 1: Check available models
        print("📋 Testing /models endpoint...")
        models_endpoints = [
            f"{vllm_url}/models",
            f"{vllm_url}/v1/models",
            f"{vllm_url.replace('/v1', '')}/models",
            f"{vllm_url.replace('/v1', '')}/v1/models"
        ]
        
        working_models_endpoint = None
        for endpoint in models_endpoints:
            try:
                async with session.get(endpoint, timeout=aiohttp.ClientTimeout(total=10)) as response:
                    if response.status == 200:
                        data = await response.json()
                        print(f"✅ Models endpoint working: {endpoint}")
                        print(f"📋 Available models: {[m.get('id', 'unknown') for m in data.get('data', [])]}")
                        working_models_endpoint = endpoint
                        break
                    else:
                        print(f"❌ {endpoint}: HTTP {response.status}")
            except Exception as e:
                print(f"❌ {endpoint}: {e}")
        
        if not working_models_endpoint:
            print("❌ No working models endpoint found!")
            return
        
        print("\n" + "=" * 60)

        # Test 1.5: Try to discover endpoints
        print("🔍 Trying to discover available endpoints...")
        discovery_endpoints = [
            "/", "/health", "/info", "/docs", "/openapi.json", "/swagger.json",
            "/api", "/api/docs", "/v1", "/v1/docs"
        ]

        for endpoint in discovery_endpoints:
            try:
                full_url = f"{vllm_url.split('/v1')[0]}{endpoint}"
                async with session.get(full_url, timeout=aiohttp.ClientTimeout(total=5)) as response:
                    if response.status == 200:
                        content_type = response.headers.get('content-type', '')
                        if 'json' in content_type:
                            try:
                                data = await response.json()
                                print(f"✅ {full_url}: JSON response with keys: {list(data.keys()) if isinstance(data, dict) else 'array'}")
                            except:
                                print(f"✅ {full_url}: JSON endpoint (couldn't parse)")
                        else:
                            text = await response.text()
                            print(f"✅ {full_url}: {content_type} ({len(text)} chars)")
                    elif response.status in [301, 302, 307, 308]:
                        location = response.headers.get('location', '')
                        print(f"🔄 {full_url}: Redirect to {location}")
            except Exception as e:
                pass  # Skip failed endpoints

        print("\n" + "=" * 60)

        # Test 2: Try different completion endpoints
        print("🚀 Testing completion endpoints...")
        
        test_prompt = "Hello, how are you?"
        
        # Different API formats to try
        api_formats = [
            # OpenAI-style completions
            {
                "endpoint": "/completions",
                "payload": {
                    "model": model_name,
                    "prompt": test_prompt,
                    "max_tokens": 50,
                    "temperature": 0.7
                }
            },
            # OpenAI-style chat completions
            {
                "endpoint": "/chat/completions", 
                "payload": {
                    "model": model_name,
                    "messages": [{"role": "user", "content": test_prompt}],
                    "max_tokens": 50,
                    "temperature": 0.7
                }
            },
            # vLLM generate endpoint
            {
                "endpoint": "/generate",
                "payload": {
                    "prompt": test_prompt,
                    "max_tokens": 50,
                    "temperature": 0.7
                }
            },
            # Text generation inference style
            {
                "endpoint": "/generate_stream",
                "payload": {
                    "inputs": test_prompt,
                    "parameters": {
                        "max_new_tokens": 50,
                        "temperature": 0.7
                    }
                }
            },
            # llama.cpp server style
            {
                "endpoint": "/completion",
                "payload": {
                    "prompt": test_prompt,
                    "n_predict": 50,
                    "temperature": 0.7
                }
            },
            # Ollama style
            {
                "endpoint": "/api/generate",
                "payload": {
                    "model": model_name,
                    "prompt": test_prompt,
                    "options": {
                        "num_predict": 50,
                        "temperature": 0.7
                    }
                }
            },
            # Text generation webui style
            {
                "endpoint": "/api/v1/generate",
                "payload": {
                    "prompt": test_prompt,
                    "max_new_tokens": 50,
                    "temperature": 0.7
                }
            },
            # Simple POST to root
            {
                "endpoint": "/",
                "payload": {
                    "prompt": test_prompt,
                    "max_tokens": 50,
                    "temperature": 0.7
                }
            }
        ]
        
        base_urls = [
            vllm_url,
            vllm_url.replace('/v1', ''),
            f"{vllm_url}/v1" if not vllm_url.endswith('/v1') else vllm_url
        ]
        
        working_endpoint = None
        working_format = None
        
        for base_url in base_urls:
            for api_format in api_formats:
                endpoint = f"{base_url.rstrip('/')}{api_format['endpoint']}"
                
                try:
                    print(f"🧪 Testing: {endpoint}")
                    async with session.post(
                        endpoint,
                        json=api_format['payload'],
                        timeout=aiohttp.ClientTimeout(total=30)
                    ) as response:
                        if response.status == 200:
                            data = await response.json()
                            print(f"✅ SUCCESS: {endpoint}")
                            print(f"📝 Response format: {list(data.keys())}")
                            
                            # Try to extract the generated text
                            if 'choices' in data and data['choices']:
                                if 'text' in data['choices'][0]:
                                    print(f"💬 Generated text: {data['choices'][0]['text'][:100]}...")
                                elif 'message' in data['choices'][0]:
                                    print(f"💬 Generated text: {data['choices'][0]['message'].get('content', '')[:100]}...")
                            elif 'generated_text' in data:
                                print(f"💬 Generated text: {data['generated_text'][:100]}...")
                            
                            working_endpoint = endpoint
                            working_format = api_format
                            break
                        else:
                            print(f"❌ {endpoint}: HTTP {response.status}")
                            
                except Exception as e:
                    print(f"❌ {endpoint}: {e}")
            
            if working_endpoint:
                break
        
        print("\n" + "=" * 60)
        
        if working_endpoint:
            print(f"🎉 FOUND WORKING API!")
            print(f"✅ Endpoint: {working_endpoint}")
            print(f"📋 Payload format: {json.dumps(working_format['payload'], indent=2)}")
            
            # Save the working configuration
            config = {
                "working_endpoint": working_endpoint,
                "api_format": working_format,
                "base_url": vllm_url,
                "model_name": model_name
            }
            
            with open("vllm_api_config.json", "w") as f:
                json.dump(config, f, indent=2)
            
            print(f"💾 Configuration saved to vllm_api_config.json")
            
        else:
            print("❌ No working completion endpoint found!")
            print("Your vLLM server might use a different API format.")
            print("Please check your vLLM server documentation.")

if __name__ == "__main__":
    asyncio.run(test_vllm_api())
