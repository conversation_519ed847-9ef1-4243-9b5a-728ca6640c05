"""Communication Manager for inter-agent coordination."""

import asyncio
import json
import logging
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime
from dataclasses import dataclass
from enum import Enum

import redis.asyncio as redis


class MessageType(Enum):
    """Types of messages in the system."""
    TASK_REQUEST = "task_request"
    TASK_RESPONSE = "task_response"
    HEARTBEAT = "heartbeat"
    STATUS_UPDATE = "status_update"
    COORDINATION = "coordination"
    ERROR = "error"


@dataclass
class Message:
    """Message structure for inter-agent communication."""
    message_id: str
    message_type: MessageType
    sender_id: str
    recipient_id: Optional[str]
    content: Dict[str, Any]
    timestamp: datetime
    priority: int = 1  # 1=low, 2=medium, 3=high


class CommunicationManager:
    """Manages communication between agents and orchestrator."""
    
    def __init__(self):
        """Initialize the communication manager."""
        self.logger = logging.getLogger("communication_manager")
        self.redis_client: Optional[redis.Redis] = None
        
        # Message handlers
        self.message_handlers: Dict[MessageType, Callable] = {}
        
        # Active subscriptions
        self.subscriptions: Dict[str, asyncio.Task] = {}
        
        # Message queues
        self.message_queues: Dict[str, List[Message]] = {}
        
        # Communication statistics
        self.stats = {
            "messages_sent": 0,
            "messages_received": 0,
            "errors": 0,
            "active_channels": 0
        }
    
    async def initialize(self, redis_client: redis.Redis):
        """Initialize the communication manager with Redis client."""
        try:
            self.redis_client = redis_client
            
            # Test Redis connection
            await self.redis_client.ping()
            
            # Setup default message handlers
            self._setup_default_handlers()
            
            # Start background tasks
            asyncio.create_task(self._message_processor())
            asyncio.create_task(self._cleanup_old_messages())
            
            self.logger.info("Communication manager initialized")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize communication manager: {e}")
            raise
    
    def _setup_default_handlers(self):
        """Setup default message handlers."""
        self.message_handlers[MessageType.HEARTBEAT] = self._handle_heartbeat
        self.message_handlers[MessageType.STATUS_UPDATE] = self._handle_status_update
        self.message_handlers[MessageType.ERROR] = self._handle_error
    
    async def send_message(
        self,
        message_type: MessageType,
        sender_id: str,
        content: Dict[str, Any],
        recipient_id: Optional[str] = None,
        priority: int = 1
    ) -> str:
        """Send a message through the communication system."""
        try:
            # Generate message ID
            message_id = f"msg_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"
            
            # Create message
            message = Message(
                message_id=message_id,
                message_type=message_type,
                sender_id=sender_id,
                recipient_id=recipient_id,
                content=content,
                timestamp=datetime.now(),
                priority=priority
            )
            
            # Determine channel
            if recipient_id:
                channel = f"agent_{recipient_id}"
            else:
                channel = f"broadcast_{message_type.value}"
            
            # Serialize message
            message_data = {
                "message_id": message.message_id,
                "message_type": message.message_type.value,
                "sender_id": message.sender_id,
                "recipient_id": message.recipient_id,
                "content": message.content,
                "timestamp": message.timestamp.isoformat(),
                "priority": message.priority
            }
            
            # Send to Redis
            await self.redis_client.lpush(channel, json.dumps(message_data))
            
            # Update statistics
            self.stats["messages_sent"] += 1
            
            self.logger.debug(f"Message {message_id} sent to {channel}")
            return message_id
            
        except Exception as e:
            self.logger.error(f"Error sending message: {e}")
            self.stats["errors"] += 1
            raise
    
    async def subscribe_to_channel(
        self,
        channel: str,
        handler: Callable[[Message], None]
    ):
        """Subscribe to a communication channel."""
        try:
            if channel in self.subscriptions:
                self.logger.warning(f"Already subscribed to channel {channel}")
                return
            
            # Create subscription task
            task = asyncio.create_task(
                self._channel_listener(channel, handler)
            )
            
            self.subscriptions[channel] = task
            self.stats["active_channels"] += 1
            
            self.logger.info(f"Subscribed to channel: {channel}")
            
        except Exception as e:
            self.logger.error(f"Error subscribing to channel {channel}: {e}")
            raise
    
    async def unsubscribe_from_channel(self, channel: str):
        """Unsubscribe from a communication channel."""
        try:
            if channel in self.subscriptions:
                task = self.subscriptions[channel]
                task.cancel()
                del self.subscriptions[channel]
                self.stats["active_channels"] -= 1
                
                self.logger.info(f"Unsubscribed from channel: {channel}")
            
        except Exception as e:
            self.logger.error(f"Error unsubscribing from channel {channel}: {e}")
    
    async def _channel_listener(self, channel: str, handler: Callable[[Message], None]):
        """Listen for messages on a specific channel."""
        try:
            while True:
                # Get message from Redis list (blocking)
                result = await self.redis_client.brpop(channel, timeout=1)
                
                if result:
                    _, message_data = result
                    
                    try:
                        # Deserialize message
                        data = json.loads(message_data)
                        message = Message(
                            message_id=data["message_id"],
                            message_type=MessageType(data["message_type"]),
                            sender_id=data["sender_id"],
                            recipient_id=data.get("recipient_id"),
                            content=data["content"],
                            timestamp=datetime.fromisoformat(data["timestamp"]),
                            priority=data.get("priority", 1)
                        )
                        
                        # Handle message
                        await self._handle_message(message, handler)
                        
                        self.stats["messages_received"] += 1
                        
                    except Exception as e:
                        self.logger.error(f"Error processing message from {channel}: {e}")
                        self.stats["errors"] += 1
                
        except asyncio.CancelledError:
            self.logger.info(f"Channel listener for {channel} cancelled")
        except Exception as e:
            self.logger.error(f"Error in channel listener for {channel}: {e}")
    
    async def _handle_message(self, message: Message, custom_handler: Callable[[Message], None]):
        """Handle an incoming message."""
        try:
            # Check if there's a specific handler for this message type
            if message.message_type in self.message_handlers:
                await self.message_handlers[message.message_type](message)
            
            # Call custom handler
            if custom_handler:
                if asyncio.iscoroutinefunction(custom_handler):
                    await custom_handler(message)
                else:
                    custom_handler(message)
            
        except Exception as e:
            self.logger.error(f"Error handling message {message.message_id}: {e}")
    
    async def _handle_heartbeat(self, message: Message):
        """Handle heartbeat messages."""
        try:
            sender_id = message.sender_id
            content = message.content
            
            # Update agent status in Redis
            agent_key = f"agent_status_{sender_id}"
            status_data = {
                "last_heartbeat": message.timestamp.isoformat(),
                "status": content.get("status", "unknown"),
                "load": content.get("load", 0),
                "memory_usage": content.get("memory_usage", 0)
            }
            
            await self.redis_client.hset(agent_key, mapping=status_data)
            
            self.logger.debug(f"Heartbeat received from {sender_id}")
            
        except Exception as e:
            self.logger.error(f"Error handling heartbeat: {e}")
    
    async def _handle_status_update(self, message: Message):
        """Handle status update messages."""
        try:
            sender_id = message.sender_id
            content = message.content
            
            # Store status update
            status_key = f"status_updates_{sender_id}"
            await self.redis_client.lpush(
                status_key,
                json.dumps({
                    "timestamp": message.timestamp.isoformat(),
                    "status": content
                })
            )
            
            # Keep only last 100 status updates
            await self.redis_client.ltrim(status_key, 0, 99)
            
            self.logger.debug(f"Status update from {sender_id}: {content.get('status', 'unknown')}")
            
        except Exception as e:
            self.logger.error(f"Error handling status update: {e}")
    
    async def _handle_error(self, message: Message):
        """Handle error messages."""
        try:
            sender_id = message.sender_id
            content = message.content
            
            # Log error
            self.logger.error(f"Error reported by {sender_id}: {content.get('error', 'Unknown error')}")
            
            # Store error for analysis
            error_key = "system_errors"
            error_data = {
                "timestamp": message.timestamp.isoformat(),
                "sender_id": sender_id,
                "error": content.get("error", "Unknown error"),
                "details": content.get("details", {})
            }
            
            await self.redis_client.lpush(error_key, json.dumps(error_data))
            
            # Keep only last 1000 errors
            await self.redis_client.ltrim(error_key, 0, 999)
            
        except Exception as e:
            self.logger.error(f"Error handling error message: {e}")
    
    async def _message_processor(self):
        """Background task to process message queues."""
        try:
            while True:
                await asyncio.sleep(1)  # Process every second
                
                # Process priority queues
                for queue_name, messages in self.message_queues.items():
                    if messages:
                        # Sort by priority (higher first)
                        messages.sort(key=lambda x: x.priority, reverse=True)
                        
                        # Process high priority messages first
                        while messages and messages[0].priority >= 3:
                            message = messages.pop(0)
                            # Process immediately
                            # Implementation depends on specific requirements
                
        except asyncio.CancelledError:
            self.logger.info("Message processor cancelled")
        except Exception as e:
            self.logger.error(f"Error in message processor: {e}")
    
    async def _cleanup_old_messages(self):
        """Background task to cleanup old messages."""
        try:
            while True:
                await asyncio.sleep(3600)  # Cleanup every hour
                
                # Cleanup old status updates, errors, etc.
                cutoff_time = datetime.now().timestamp() - 86400  # 24 hours ago
                
                # This is a simplified cleanup - in production you'd want more sophisticated logic
                keys_to_cleanup = [
                    "system_errors",
                    "status_updates_*",
                    "agent_status_*"
                ]
                
                for pattern in keys_to_cleanup:
                    if "*" in pattern:
                        keys = await self.redis_client.keys(pattern)
                        for key in keys:
                            # Cleanup old entries
                            await self.redis_client.ltrim(key, 0, 99)
                    else:
                        await self.redis_client.ltrim(pattern, 0, 999)
                
                self.logger.debug("Completed message cleanup")
                
        except asyncio.CancelledError:
            self.logger.info("Message cleanup cancelled")
        except Exception as e:
            self.logger.error(f"Error in message cleanup: {e}")
    
    async def broadcast_message(
        self,
        message_type: MessageType,
        sender_id: str,
        content: Dict[str, Any],
        priority: int = 1
    ) -> str:
        """Broadcast a message to all agents."""
        return await self.send_message(
            message_type=message_type,
            sender_id=sender_id,
            content=content,
            recipient_id=None,  # Broadcast
            priority=priority
        )
    
    async def get_agent_status(self, agent_id: str) -> Optional[Dict[str, Any]]:
        """Get the current status of an agent."""
        try:
            agent_key = f"agent_status_{agent_id}"
            status_data = await self.redis_client.hgetall(agent_key)
            
            if status_data:
                return {k.decode(): v.decode() for k, v in status_data.items()}
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error getting agent status for {agent_id}: {e}")
            return None
    
    async def get_communication_stats(self) -> Dict[str, Any]:
        """Get communication statistics."""
        return {
            **self.stats,
            "active_subscriptions": list(self.subscriptions.keys()),
            "queue_sizes": {name: len(queue) for name, queue in self.message_queues.items()}
        }
    
    async def cleanup(self):
        """Cleanup communication manager resources."""
        try:
            # Cancel all subscription tasks
            for channel, task in self.subscriptions.items():
                task.cancel()
                self.logger.debug(f"Cancelled subscription to {channel}")
            
            self.subscriptions.clear()
            self.message_queues.clear()
            
            self.logger.info("Communication manager cleanup completed")
            
        except Exception as e:
            self.logger.error(f"Error during communication manager cleanup: {e}")
