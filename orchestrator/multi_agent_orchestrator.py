"""Multi-Agent Orchestrator for BitNet and Reasoning Agent coordination."""

import os
import sys
import json
import asyncio
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path
import logging
from datetime import datetime
from enum import Enum

import aiohttp
import redis.asyncio as redis
from fastapi import FastAPI, HTTPException, BackgroundTasks
from pydantic import BaseModel
import uvicorn

# Add parent directories to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from config.bitnet_config import BitNetConfig
from agents.reasoning_agent import ReasoningAgent, ReasoningRequest
from orchestrator.communication_manager import CommunicationManager


class TaskType(Enum):
    """Types of tasks the orchestrator can handle."""
    ANALYSIS = "analysis"
    CODE_GENERATION = "code_generation"
    CODE_REVIEW = "code_review"
    RESEARCH = "research"
    GENERAL = "general"


@dataclass
class TaskResult:
    """Result of a multi-agent task."""
    task_id: str
    task_type: TaskType
    final_result: str
    reasoning_steps: List[str]
    confidence_score: float
    agent_contributions: Dict[str, Any]
    processing_time: float
    status: str


class MultiAgentTask(BaseModel):
    """Request model for multi-agent tasks."""
    task_description: str
    task_type: str = "general"
    context: Optional[Dict[str, Any]] = None
    require_all_agents: bool = False
    enable_parallel_processing: bool = True
    max_context_length: Optional[int] = None


class MultiAgentOrchestrator:
    """Orchestrator for coordinating BitNet agents and reasoning model."""
    
    def __init__(self):
        """Initialize the orchestrator."""
        self.config = BitNetConfig()
        
        # Setup logging
        self.logger = self._setup_logging()
        
        # Initialize components
        self.reasoning_agent = ReasoningAgent()
        self.communication_manager = CommunicationManager()
        
        # Redis client for agent coordination
        self.redis_client = None
        
        # Active tasks
        self.active_tasks: Dict[str, TaskResult] = {}
        
        # Agent registry
        self.available_agents: Dict[str, Dict[str, Any]] = {}
        
        self.logger.info("Multi-Agent Orchestrator initialized")
    
    def _setup_logging(self) -> logging.Logger:
        """Setup logging for the orchestrator."""
        logger = logging.getLogger("orchestrator")
        logger.setLevel(logging.INFO)
        
        # Create logs directory if it doesn't exist
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # File handler
        file_handler = logging.FileHandler(log_dir / "orchestrator.log")
        file_handler.setLevel(logging.INFO)
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # Formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        return logger
    
    async def initialize(self):
        """Initialize the orchestrator and its connections."""
        try:
            # Initialize Redis connection
            self.redis_client = redis.from_url(self.config.REDIS_URL)
            await self.redis_client.ping()
            self.logger.info("Redis connection established")
            
            # Initialize communication manager
            await self.communication_manager.initialize(self.redis_client)
            
            # Discover available agents
            await self._discover_agents()
            
            # Start background tasks
            asyncio.create_task(self._monitor_agents())
            
            self.logger.info("Orchestrator initialization completed")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize orchestrator: {e}")
            raise
    
    async def _discover_agents(self):
        """Discover available BitNet agents."""
        try:
            # Get registered agents from Redis
            agents_data = await self.redis_client.hgetall("bitnet_agents")
            
            for agent_id, agent_info_str in agents_data.items():
                agent_info = json.loads(agent_info_str)
                self.available_agents[agent_id.decode()] = agent_info
                self.logger.info(f"Discovered agent: {agent_id.decode()} ({agent_info['agent_role']})")
            
            if not self.available_agents:
                self.logger.warning("No BitNet agents discovered")
            
        except Exception as e:
            self.logger.error(f"Error discovering agents: {e}")
    
    async def _monitor_agents(self):
        """Monitor agent health and availability."""
        while True:
            try:
                await asyncio.sleep(self.config.RESOURCE_CHECK_INTERVAL)
                
                # Check agent heartbeats
                current_time = datetime.now()
                inactive_agents = []
                
                for agent_id, agent_info in self.available_agents.items():
                    last_heartbeat = datetime.fromisoformat(agent_info.get("last_heartbeat", "1970-01-01"))
                    time_diff = (current_time - last_heartbeat).total_seconds()
                    
                    if time_diff > 60:  # 1 minute timeout
                        inactive_agents.append(agent_id)
                        agent_info["status"] = "inactive"
                    else:
                        agent_info["status"] = "active"
                
                if inactive_agents:
                    self.logger.warning(f"Inactive agents detected: {inactive_agents}")
                
                # Rediscover agents periodically
                if len(self.available_agents) == 0:
                    await self._discover_agents()
                
            except Exception as e:
                self.logger.error(f"Error in agent monitoring: {e}")
    
    async def process_task(self, task: MultiAgentTask) -> TaskResult:
        """Process a multi-agent task."""
        task_id = f"task_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"
        start_time = datetime.now()
        
        try:
            self.logger.info(f"Processing task {task_id}: {task.task_description[:100]}...")
            
            # Determine task type
            task_type = TaskType(task.task_type)
            
            # Select appropriate agents
            selected_agents = self._select_agents_for_task(task_type, task.require_all_agents)
            
            if not selected_agents:
                raise Exception("No suitable agents available for this task")
            
            # Distribute task to BitNet agents
            bitnet_responses = await self._distribute_task_to_agents(
                task, selected_agents, task.enable_parallel_processing
            )
            
            # Process with reasoning agent
            reasoning_request = ReasoningRequest(
                task_description=task.task_description,
                bitnet_responses=bitnet_responses,
                context=task.context,
                require_code=(task_type == TaskType.CODE_GENERATION),
                require_analysis=(task_type == TaskType.ANALYSIS)
            )
            
            reasoning_response = await self.reasoning_agent.process_reasoning_request(reasoning_request)
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            # Create task result
            result = TaskResult(
                task_id=task_id,
                task_type=task_type,
                final_result=reasoning_response.final_decision,
                reasoning_steps=reasoning_response.reasoning_steps,
                confidence_score=reasoning_response.confidence_score,
                agent_contributions=reasoning_response.agent_contributions,
                processing_time=processing_time,
                status="completed"
            )
            
            self.active_tasks[task_id] = result
            
            self.logger.info(
                f"Task {task_id} completed - Confidence: {result.confidence_score:.2f}, "
                f"Time: {processing_time:.2f}s"
            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error processing task {task_id}: {e}")
            
            error_result = TaskResult(
                task_id=task_id,
                task_type=TaskType.GENERAL,
                final_result=f"Error processing task: {str(e)}",
                reasoning_steps=[],
                confidence_score=0.0,
                agent_contributions={},
                processing_time=(datetime.now() - start_time).total_seconds(),
                status="error"
            )
            
            self.active_tasks[task_id] = error_result
            return error_result
    
    def _select_agents_for_task(self, task_type: TaskType, require_all: bool) -> List[str]:
        """Select appropriate agents for a given task type."""
        active_agents = {
            agent_id: info for agent_id, info in self.available_agents.items()
            if info.get("status") == "active"
        }
        
        if require_all:
            return list(active_agents.keys())
        
        # Task-specific agent selection
        preferred_roles = {
            TaskType.ANALYSIS: ["analyzer", "researcher"],
            TaskType.CODE_GENERATION: ["coder", "analyzer"],
            TaskType.CODE_REVIEW: ["reviewer", "coder"],
            TaskType.RESEARCH: ["researcher", "analyzer"],
            TaskType.GENERAL: ["analyzer", "coder", "reviewer", "researcher"]
        }
        
        selected = []
        target_roles = preferred_roles.get(task_type, ["analyzer"])
        
        for agent_id, agent_info in active_agents.items():
            agent_role = agent_info.get("agent_role", "")
            if agent_role in target_roles:
                selected.append(agent_id)
        
        # If no specific agents found, use any available
        if not selected:
            selected = list(active_agents.keys())
        
        return selected[:self.config.BITNET_AGENT_COUNT]  # Limit to configured count
    
    async def _distribute_task_to_agents(
        self,
        task: MultiAgentTask,
        selected_agents: List[str],
        parallel: bool
    ) -> Dict[str, Any]:
        """Distribute task to selected BitNet agents."""
        responses = {}
        
        try:
            if parallel and self.config.PARALLEL_PROCESSING:
                # Parallel processing
                tasks = []
                for agent_id in selected_agents:
                    agent_info = self.available_agents[agent_id]
                    task_coroutine = self._send_task_to_agent(task, agent_info)
                    tasks.append((agent_id, task_coroutine))
                
                # Execute all tasks concurrently
                results = await asyncio.gather(
                    *[task_coro for _, task_coro in tasks],
                    return_exceptions=True
                )
                
                # Collect results
                for (agent_id, _), result in zip(tasks, results):
                    if isinstance(result, Exception):
                        self.logger.error(f"Agent {agent_id} failed: {result}")
                        responses[agent_id] = {"error": str(result)}
                    else:
                        responses[agent_id] = result
            else:
                # Sequential processing
                for agent_id in selected_agents:
                    try:
                        agent_info = self.available_agents[agent_id]
                        response = await self._send_task_to_agent(task, agent_info)
                        responses[agent_id] = response
                    except Exception as e:
                        self.logger.error(f"Agent {agent_id} failed: {e}")
                        responses[agent_id] = {"error": str(e)}
            
            return responses
            
        except Exception as e:
            self.logger.error(f"Error distributing task to agents: {e}")
            raise
    
    async def _send_task_to_agent(self, task: MultiAgentTask, agent_info: Dict[str, Any]) -> Dict[str, Any]:
        """Send task to a specific BitNet agent."""
        try:
            endpoint = agent_info.get("endpoint", "")
            if not endpoint:
                raise Exception(f"No endpoint for agent {agent_info.get('agent_id')}")
            
            # Prepare request
            request_data = {
                "prompt": task.task_description,
                "max_tokens": task.max_context_length or self.config.BITNET_MAX_TOKENS,
                "temperature": self.config.BITNET_TEMPERATURE,
                "enable_cot": self.config.ENABLE_CHAIN_OF_THOUGHT,
                "context": task.context
            }
            
            # Send request to agent
            timeout = aiohttp.ClientTimeout(total=self.config.AGENT_COMMUNICATION_TIMEOUT)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.post(f"{endpoint}/inference", json=request_data) as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        error_text = await response.text()
                        raise Exception(f"Agent request failed: {response.status} - {error_text}")
            
        except Exception as e:
            self.logger.error(f"Error sending task to agent: {e}")
            raise
    
    async def get_task_status(self, task_id: str) -> Optional[TaskResult]:
        """Get the status of a specific task."""
        return self.active_tasks.get(task_id)
    
    async def get_system_status(self) -> Dict[str, Any]:
        """Get overall system status."""
        try:
            # Agent status
            active_agents = sum(1 for info in self.available_agents.values() if info.get("status") == "active")
            
            # Reasoning agent status
            reasoning_health = await self.reasoning_agent.health_check()
            
            # Task statistics
            completed_tasks = sum(1 for task in self.active_tasks.values() if task.status == "completed")
            error_tasks = sum(1 for task in self.active_tasks.values() if task.status == "error")
            
            return {
                "status": "healthy" if active_agents > 0 and reasoning_health["status"] == "healthy" else "degraded",
                "agents": {
                    "total_registered": len(self.available_agents),
                    "active": active_agents,
                    "agents_list": list(self.available_agents.keys())
                },
                "reasoning_agent": reasoning_health,
                "tasks": {
                    "total": len(self.active_tasks),
                    "completed": completed_tasks,
                    "errors": error_tasks
                },
                "configuration": {
                    "parallel_processing": self.config.PARALLEL_PROCESSING,
                    "chain_of_thought": self.config.ENABLE_CHAIN_OF_THOUGHT,
                    "mixture_of_experts": self.config.ENABLE_MIXTURE_OF_EXPERTS
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error getting system status: {e}")
            return {"status": "error", "error": str(e)}
    
    async def cleanup(self):
        """Cleanup resources."""
        try:
            if self.redis_client:
                await self.redis_client.close()
            await self.communication_manager.cleanup()
            self.logger.info("Orchestrator cleanup completed")
        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")


# FastAPI app for the orchestrator
app = FastAPI(title="BitNet Multi-Agent Orchestrator", version="1.0.0")

# Global orchestrator instance
orchestrator: Optional[MultiAgentOrchestrator] = None


@app.on_event("startup")
async def startup_event():
    """Initialize the orchestrator on startup."""
    global orchestrator
    orchestrator = MultiAgentOrchestrator()
    await orchestrator.initialize()


@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown."""
    global orchestrator
    if orchestrator:
        await orchestrator.cleanup()


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    if not orchestrator:
        raise HTTPException(status_code=503, detail="Orchestrator not initialized")
    
    status = await orchestrator.get_system_status()
    return status


@app.post("/process")
async def process_task_endpoint(task: MultiAgentTask):
    """Process a multi-agent task."""
    if not orchestrator:
        raise HTTPException(status_code=503, detail="Orchestrator not initialized")
    
    result = await orchestrator.process_task(task)
    return result


@app.get("/task/{task_id}")
async def get_task_status_endpoint(task_id: str):
    """Get task status."""
    if not orchestrator:
        raise HTTPException(status_code=503, detail="Orchestrator not initialized")
    
    result = await orchestrator.get_task_status(task_id)
    if not result:
        raise HTTPException(status_code=404, detail="Task not found")
    
    return result


@app.get("/status")
async def system_status_endpoint():
    """Get system status."""
    if not orchestrator:
        raise HTTPException(status_code=503, detail="Orchestrator not initialized")
    
    return await orchestrator.get_system_status()


if __name__ == "__main__":
    # Run the orchestrator server
    port = int(os.getenv("ORCHESTRATOR_PORT", "9200"))
    uvicorn.run(app, host="0.0.0.0", port=port)
