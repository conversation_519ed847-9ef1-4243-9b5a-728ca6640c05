# BitNet Multi-Agent System Requirements

# Core framework dependencies
fastapi>=0.104.1
uvicorn>=0.24.0
pydantic>=2.6.4

# Async and networking
aiohttp>=3.9.1
asyncio-mqtt>=0.16.1
websockets>=12.0

# Redis for communication
redis>=5.0.1
celery>=5.3.4

# Environment and configuration
python-dotenv>=1.0.1

# AI/ML dependencies
transformers>=4.36.2
torch>=2.1.2
tiktoken>=0.6.0
huggingface-hub>=0.19.4

# LangChain for reasoning agent (existing multi-agent workflow)
langchain>=0.2.16
langchain-community>=0.2.17
langchain-core>=0.2.40
langgraph>=0.1.19

# OpenAI compatibility
openai>=1.14.3

# Utilities
requests>=2.31.0
numpy>=1.24.3
psutil>=5.9.6
json5>=0.9.14
typing-extensions>=4.10.0

# Development and testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
black>=23.0.0
flake8>=6.0.0

# Optional: Streamlit for GUI (from existing multi-agent workflow)
streamlit>=1.32.0
