# BitNet Multi-Agent System Configuration

# ============================================================================
# BITNET AGENTS CONFIGURATION
# ============================================================================

# Number of BitNet agents to spawn
BITNET_AGENT_COUNT=4

# BitNet model configuration
BITNET_MODEL_NAME=microsoft/BitNet-b1.58-2B-4T
BITNET_MODEL_PATH=./models/BitNet-b1.58-2B-4T
BITNET_QUANTIZATION_TYPE=i2_s

# BitNet agent specializations (comma-separated)
BITNET_AGENT_ROLES=analyzer,coder,reviewer,researcher

# BitNet inference settings
BITNET_MAX_TOKENS=32000
BITNET_TEMPERATURE=0.7
BITNET_TOP_P=0.9
BITNET_THREADS=4

# ============================================================================
# REASONING MODEL CONFIGURATION (vLLM)
# ============================================================================

# Reasoning model settings
REASONING_MODEL_URL=http://localhost:8001/v1
REASONING_MODEL_NAME=llama-3.1-8b-instruct
REASONING_MODEL_API_KEY=your-api-key-here

# Reasoning model parameters
REASONING_MAX_TOKENS=8196
REASONING_TEMPERATURE=0.3
REASONING_TOP_P=0.95

# ============================================================================
# DOCKER CONFIGURATION
# ============================================================================

# Docker settings
DOCKER_ENABLED=true
BITNET_DOCKER_IMAGE=bitnet-agent:latest
DOCKER_NETWORK=bitnet-network

# Container resource limits
BITNET_CONTAINER_CPU_LIMIT=2
BITNET_CONTAINER_MEMORY_LIMIT=4g

# ============================================================================
# ORCHESTRATION SETTINGS
# ============================================================================

# Communication settings
AGENT_COMMUNICATION_TIMEOUT=30
MAX_AGENT_RETRIES=3
ENABLE_CHAIN_OF_THOUGHT=true
ENABLE_MIXTURE_OF_EXPERTS=true

# Task distribution
MAX_CONTEXT_LENGTH=32768
CONTEXT_OVERLAP=512
PARALLEL_PROCESSING=true

# ============================================================================
# MEMORY AND LOGGING
# ============================================================================

# Memory configuration
MEMORY_FOLDER=./memory_logs
ENABLE_PERSISTENT_MEMORY=true
MEMORY_CONSOLIDATION_INTERVAL=100

# Logging
LOG_LEVEL=INFO
ENABLE_DETAILED_LOGGING=true
LOG_FOLDER=./logs

# ============================================================================
# PERFORMANCE TUNING
# ============================================================================

# Performance settings
ENABLE_CACHING=true
CACHE_SIZE=1000
BATCH_SIZE=4
ASYNC_PROCESSING=true

# Resource monitoring
ENABLE_RESOURCE_MONITORING=true
RESOURCE_CHECK_INTERVAL=10

# ============================================================================
# SECURITY AND NETWORKING
# ============================================================================

# Network settings
BITNET_BASE_PORT=9000
REASONING_AGENT_PORT=9100
ORCHESTRATOR_PORT=9200

# Security
ENABLE_API_AUTHENTICATION=false
API_SECRET_KEY=your-secret-key-here
