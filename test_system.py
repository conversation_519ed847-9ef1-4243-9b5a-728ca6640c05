#!/usr/bin/env python3
"""Test script for BitNet Multi-Agent System."""

import asyncio
import sys
import logging
from pathlib import Path

# Add current directory to path
sys.path.append(str(Path(__file__).parent))

from config.bitnet_config import BitNetConfig
from orchestrator.multi_agent_orchestrator import MultiAgentOrchestrator, MultiAgentTask


async def test_configuration():
    """Test system configuration."""
    print("Testing configuration...")
    
    try:
        config = BitNetConfig()
        errors = config.validate_config()
        
        if errors:
            print("❌ Configuration validation failed:")
            for error in errors:
                print(f"   - {error}")
            return False
        else:
            print("✅ Configuration validation passed")
            return True
            
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False


async def test_orchestrator_initialization():
    """Test orchestrator initialization."""
    print("Testing orchestrator initialization...")
    
    try:
        orchestrator = MultiAgentOrchestrator()
        await orchestrator.initialize()
        
        status = await orchestrator.get_system_status()
        print(f"✅ Orchestrator initialized - Status: {status.get('status', 'unknown')}")
        
        await orchestrator.cleanup()
        return True
        
    except Exception as e:
        print(f"❌ Orchestrator initialization failed: {e}")
        return False


async def test_simple_task():
    """Test a simple task processing."""
    print("Testing simple task processing...")
    
    try:
        orchestrator = MultiAgentOrchestrator()
        await orchestrator.initialize()
        
        # Create a simple test task
        task = MultiAgentTask(
            task_description="Explain what a sorting algorithm is in simple terms",
            task_type="analysis"
        )
        
        print("Processing test task...")
        result = await orchestrator.process_task(task)
        
        print(f"✅ Task completed successfully")
        print(f"   Task ID: {result.task_id}")
        print(f"   Confidence: {result.confidence_score:.2f}")
        print(f"   Processing time: {result.processing_time:.2f}s")
        print(f"   Result preview: {result.final_result[:100]}...")
        
        await orchestrator.cleanup()
        return True
        
    except Exception as e:
        print(f"❌ Task processing failed: {e}")
        return False


async def test_agent_discovery():
    """Test agent discovery and communication."""
    print("Testing agent discovery...")
    
    try:
        orchestrator = MultiAgentOrchestrator()
        await orchestrator.initialize()
        
        # Wait a bit for agents to register
        await asyncio.sleep(5)
        
        status = await orchestrator.get_system_status()
        agents_info = status.get('agents', {})
        
        active_agents = agents_info.get('active', 0)
        total_agents = agents_info.get('total_registered', 0)
        
        print(f"   Total registered agents: {total_agents}")
        print(f"   Active agents: {active_agents}")
        
        if active_agents > 0:
            print("✅ Agent discovery successful")
            agents_list = agents_info.get('agents_list', [])
            for agent in agents_list:
                print(f"   - {agent}")
            result = True
        else:
            print("⚠️  No active agents found - this may be expected if agents aren't running")
            result = True  # Not necessarily a failure
        
        await orchestrator.cleanup()
        return result
        
    except Exception as e:
        print(f"❌ Agent discovery failed: {e}")
        return False


async def test_reasoning_agent():
    """Test reasoning agent functionality."""
    print("Testing reasoning agent...")
    
    try:
        from agents.reasoning_agent import ReasoningAgent, ReasoningRequest
        
        reasoning_agent = ReasoningAgent()
        
        # Test health check
        health = await reasoning_agent.health_check()
        print(f"   Reasoning agent health: {health.get('status', 'unknown')}")
        
        if health.get('status') == 'healthy':
            # Test simple reasoning
            request = ReasoningRequest(
                task_description="Compare two sorting algorithms",
                bitnet_responses={
                    "analyzer": {
                        "content": "Bubble sort is simple but inefficient with O(n²) complexity",
                        "chain_of_thought": ["Analyzing complexity", "Considering use cases"]
                    },
                    "coder": {
                        "content": "Quick sort is more efficient with O(n log n) average complexity",
                        "chain_of_thought": ["Implementing algorithm", "Testing performance"]
                    }
                }
            )
            
            response = await reasoning_agent.process_reasoning_request(request)
            
            print(f"✅ Reasoning agent test successful")
            print(f"   Confidence: {response.confidence_score:.2f}")
            print(f"   Reasoning steps: {len(response.reasoning_steps)}")
            return True
        else:
            print("⚠️  Reasoning agent not healthy - check vLLM server connection")
            return True  # Not necessarily a failure if vLLM isn't running
            
    except Exception as e:
        print(f"❌ Reasoning agent test failed: {e}")
        return False


async def run_all_tests():
    """Run all tests."""
    print("🧪 BitNet Multi-Agent System Test Suite")
    print("=" * 50)
    
    tests = [
        ("Configuration", test_configuration),
        ("Orchestrator Initialization", test_orchestrator_initialization),
        ("Agent Discovery", test_agent_discovery),
        ("Reasoning Agent", test_reasoning_agent),
        ("Simple Task Processing", test_simple_task),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}")
        print("-" * 30)
        
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test '{test_name}' crashed: {e}")
            results.append((test_name, False))
    
    # Print summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed!")
        return True
    else:
        print("⚠️  Some tests failed - check the output above")
        return False


async def main():
    """Main test function."""
    # Setup logging to reduce noise during tests
    logging.getLogger().setLevel(logging.WARNING)
    
    success = await run_all_tests()
    
    if success:
        print("\n✅ System appears to be working correctly!")
        sys.exit(0)
    else:
        print("\n❌ Some issues detected - please check the configuration and setup")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
