"""Reasoning Agent using vLLM for final decision making."""

import os
import sys
import json
import asyncio
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from pathlib import Path
import logging
from datetime import datetime

import aiohttp
from langchain_community.llms.vllm import VLLMOpenAI
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from pydantic import BaseModel

# Add parent directories to path for imports
sys.path.append(str(Path(__file__).parent.parent))
sys.path.append(str(Path(__file__).parent.parent / "multi-agent-workflow"))

from config.bitnet_config import BitNetConfig
from config import Config as MultiAgentConfig


@dataclass
class ReasoningResponse:
    """Response from reasoning agent."""
    final_decision: str
    reasoning_steps: List[str]
    confidence_score: float
    agent_contributions: Dict[str, str]
    processing_time: float


class ReasoningRequest(BaseModel):
    """Request for reasoning agent."""
    task_description: str
    bitnet_responses: Dict[str, Any]
    context: Optional[Dict[str, Any]] = None
    require_code: bool = False
    require_analysis: bool = False


class ReasoningAgent:
    """Advanced reasoning agent using vLLM for final decision making."""
    
    def __init__(self):
        """Initialize the reasoning agent."""
        self.config = BitNetConfig()
        self.multi_agent_config = MultiAgentConfig()
        
        # Setup logging
        self.logger = self._setup_logging()
        
        # Initialize vLLM client
        self.llm = None
        self._initialize_llm()
        
        self.logger.info("Reasoning Agent initialized")
    
    def _setup_logging(self) -> logging.Logger:
        """Setup logging for the reasoning agent."""
        logger = logging.getLogger("reasoning_agent")
        logger.setLevel(logging.INFO)
        
        # Create logs directory if it doesn't exist
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # File handler
        file_handler = logging.FileHandler(log_dir / "reasoning_agent.log")
        file_handler.setLevel(logging.INFO)
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # Formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        return logger
    
    def _initialize_llm(self):
        """Initialize the vLLM client."""
        try:
            reasoning_config = self.config.get_reasoning_config()
            
            self.llm = VLLMOpenAI(
                openai_api_key=reasoning_config["api_key"],
                openai_api_base=reasoning_config["base_url"],
                model_name=reasoning_config["model"],
                temperature=reasoning_config["temperature"],
                max_tokens=reasoning_config["max_tokens"],
                top_p=reasoning_config["top_p"]
            )
            
            # Test the connection
            test_response = self.llm.invoke("Hello, this is a test.")
            if test_response:
                self.logger.info("vLLM reasoning model initialized successfully")
            else:
                raise Exception("Failed to get response from vLLM model")
                
        except Exception as e:
            self.logger.error(f"Failed to initialize vLLM model: {e}")
            self.llm = None
    
    async def process_reasoning_request(self, request: ReasoningRequest) -> ReasoningResponse:
        """Process a reasoning request using BitNet agent outputs."""
        start_time = datetime.now()
        
        try:
            if not self.llm:
                raise Exception("Reasoning model not available")
            
            # Analyze BitNet responses
            agent_analysis = self._analyze_bitnet_responses(request.bitnet_responses)
            
            # Build reasoning prompt
            reasoning_prompt = self._build_reasoning_prompt(
                request.task_description,
                agent_analysis,
                request.context,
                request.require_code,
                request.require_analysis
            )
            
            # Generate reasoning response
            reasoning_response = await self._generate_reasoning(reasoning_prompt)
            
            # Parse reasoning steps and final decision
            reasoning_steps, final_decision, confidence = self._parse_reasoning_response(
                reasoning_response
            )
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            self.logger.info(
                f"Reasoning completed - Confidence: {confidence:.2f}, "
                f"Time: {processing_time:.2f}s"
            )
            
            return ReasoningResponse(
                final_decision=final_decision,
                reasoning_steps=reasoning_steps,
                confidence_score=confidence,
                agent_contributions=agent_analysis,
                processing_time=processing_time
            )
            
        except Exception as e:
            self.logger.error(f"Error in reasoning process: {e}")
            raise
    
    def _analyze_bitnet_responses(self, bitnet_responses: Dict[str, Any]) -> Dict[str, str]:
        """Analyze and summarize BitNet agent responses."""
        analysis = {}
        
        for agent_role, response_data in bitnet_responses.items():
            if isinstance(response_data, dict):
                content = response_data.get("content", "")
                cot = response_data.get("chain_of_thought", [])
                
                # Summarize the agent's contribution
                summary = f"Agent Role: {agent_role}\n"
                summary += f"Response: {content[:500]}...\n" if len(content) > 500 else f"Response: {content}\n"
                
                if cot:
                    summary += f"Chain of Thought: {' -> '.join(cot[:3])}\n"
                
                analysis[agent_role] = summary
            else:
                analysis[agent_role] = str(response_data)
        
        return analysis
    
    def _build_reasoning_prompt(
        self,
        task_description: str,
        agent_analysis: Dict[str, str],
        context: Optional[Dict[str, Any]],
        require_code: bool,
        require_analysis: bool
    ) -> str:
        """Build the reasoning prompt for the vLLM model."""
        
        prompt = f"""You are an advanced reasoning agent that synthesizes outputs from multiple 1-bit BitNet agents to make final decisions.

TASK DESCRIPTION:
{task_description}

BITNET AGENT CONTRIBUTIONS:
"""
        
        for agent_role, analysis in agent_analysis.items():
            prompt += f"\n--- {agent_role.upper()} AGENT ---\n{analysis}\n"
        
        if context:
            prompt += f"\nADDITIONAL CONTEXT:\n{json.dumps(context, indent=2)}\n"
        
        prompt += """
REASONING INSTRUCTIONS:
1. Analyze each agent's contribution carefully
2. Identify strengths and weaknesses in each response
3. Look for consensus and conflicts between agents
4. Apply advanced reasoning to synthesize the best solution
5. Provide step-by-step reasoning for your decision
6. Assign a confidence score (0.0-1.0) to your final decision

"""
        
        if require_code:
            prompt += "7. Generate clean, production-ready code if applicable\n"
        
        if require_analysis:
            prompt += "8. Provide detailed technical analysis\n"
        
        prompt += """
RESPONSE FORMAT:
REASONING STEPS:
1. [First reasoning step]
2. [Second reasoning step]
3. [Continue as needed]

CONFIDENCE SCORE: [0.0-1.0]

FINAL DECISION:
[Your synthesized final decision/solution]
"""
        
        return prompt
    
    async def _generate_reasoning(self, prompt: str) -> str:
        """Generate reasoning response using vLLM."""
        try:
            # Use asyncio to run the synchronous LLM call
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(None, self.llm.invoke, prompt)
            return response.strip()
            
        except Exception as e:
            self.logger.error(f"Error generating reasoning response: {e}")
            raise
    
    def _parse_reasoning_response(self, response: str) -> tuple[List[str], str, float]:
        """Parse the reasoning response to extract steps, decision, and confidence."""
        reasoning_steps = []
        final_decision = ""
        confidence = 0.5  # Default confidence
        
        try:
            lines = response.split('\n')
            current_section = None
            
            for line in lines:
                line = line.strip()
                
                if line.startswith("REASONING STEPS:"):
                    current_section = "reasoning"
                    continue
                elif line.startswith("CONFIDENCE SCORE:"):
                    current_section = "confidence"
                    # Extract confidence score
                    try:
                        confidence_str = line.split(":", 1)[1].strip()
                        confidence = float(confidence_str)
                    except:
                        confidence = 0.5
                    continue
                elif line.startswith("FINAL DECISION:"):
                    current_section = "decision"
                    continue
                
                if current_section == "reasoning" and line:
                    if line[0].isdigit() or line.startswith("-"):
                        reasoning_steps.append(line)
                elif current_section == "decision" and line:
                    final_decision += line + "\n"
            
            final_decision = final_decision.strip()
            
            # Ensure we have at least some content
            if not final_decision:
                final_decision = response
            
            if not reasoning_steps:
                reasoning_steps = ["Analyzed all agent contributions", "Applied reasoning synthesis"]
            
        except Exception as e:
            self.logger.error(f"Error parsing reasoning response: {e}")
            final_decision = response
            reasoning_steps = ["Error in parsing reasoning steps"]
        
        return reasoning_steps, final_decision, confidence
    
    async def health_check(self) -> Dict[str, Any]:
        """Check the health of the reasoning agent."""
        try:
            if not self.llm:
                return {"status": "unhealthy", "reason": "LLM not initialized"}
            
            # Quick test
            test_response = await self._generate_reasoning("Test prompt")
            
            return {
                "status": "healthy",
                "model": self.config.REASONING_MODEL_NAME,
                "test_response_length": len(test_response)
            }
            
        except Exception as e:
            return {"status": "unhealthy", "reason": str(e)}
    
    def get_capabilities(self) -> Dict[str, Any]:
        """Get the capabilities of the reasoning agent."""
        return {
            "model": self.config.REASONING_MODEL_NAME,
            "max_tokens": self.config.REASONING_MAX_TOKENS,
            "supports_code_generation": True,
            "supports_analysis": True,
            "supports_synthesis": True,
            "confidence_scoring": True
        }
