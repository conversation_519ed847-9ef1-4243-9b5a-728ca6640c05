"""BitNet Agent Implementation for 1-bit LLM processing."""

import os
import sys
import json
import asyncio
import subprocess
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from pathlib import Path
import logging
from datetime import datetime

import aiohttp
import redis.asyncio as redis
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
import uvicorn

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from config.bitnet_config import BitNetConfig


@dataclass
class BitNetResponse:
    """Response from BitNet inference."""
    content: str
    tokens_generated: int
    processing_time: float
    agent_id: str
    agent_role: str
    chain_of_thought: List[str]


class BitNetInferenceRequest(BaseModel):
    """Request model for BitNet inference."""
    prompt: str
    max_tokens: int = 2048
    temperature: float = 0.7
    enable_cot: bool = True
    context: Optional[Dict[str, Any]] = None


class BitNetAgent:
    """BitNet 1.58-bit LLM Agent for efficient CPU inference."""
    
    def __init__(self, agent_id: str, agent_role: str, model_path: str):
        """Initialize BitNet agent."""
        self.agent_id = agent_id
        self.agent_role = agent_role
        self.model_path = model_path
        self.config = BitNetConfig()
        
        # Setup logging
        self.logger = self._setup_logging()
        
        # Initialize Redis connection
        self.redis_client = None
        
        # BitNet process
        self.bitnet_process = None
        
        # Agent specialization prompts
        self.role_prompts = {
            "analyzer": "You are an expert analyzer. Break down complex problems into components and provide detailed analysis.",
            "coder": "You are an expert programmer. Generate clean, efficient, and well-documented code solutions.",
            "reviewer": "You are an expert code reviewer. Analyze code for quality, security, and best practices.",
            "researcher": "You are an expert researcher. Gather information, synthesize findings, and provide insights."
        }
        
        self.logger.info(f"BitNet Agent {agent_id} ({agent_role}) initialized")
    
    def _setup_logging(self) -> logging.Logger:
        """Setup logging for the agent."""
        logger = logging.getLogger(f"bitnet_agent_{self.agent_id}")
        logger.setLevel(logging.INFO)
        
        # Create logs directory if it doesn't exist
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # File handler
        file_handler = logging.FileHandler(
            log_dir / f"bitnet_agent_{self.agent_id}.log"
        )
        file_handler.setLevel(logging.INFO)
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # Formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        return logger
    
    async def initialize(self):
        """Initialize the agent and its connections."""
        try:
            # Initialize Redis connection
            redis_url = os.getenv("REDIS_URL", "redis://localhost:6379")
            self.redis_client = redis.from_url(redis_url)
            await self.redis_client.ping()
            self.logger.info("Redis connection established")
            
            # Initialize BitNet model
            await self._initialize_bitnet_model()
            
            # Register agent with orchestrator
            await self._register_with_orchestrator()
            
        except Exception as e:
            self.logger.error(f"Failed to initialize agent: {e}")
            raise
    
    async def _initialize_bitnet_model(self):
        """Initialize the BitNet model for inference."""
        try:
            # Check if model exists
            model_file = Path(self.model_path) / "ggml-model-i2_s.gguf"
            if not model_file.exists():
                self.logger.error(f"BitNet model not found at {model_file}")
                raise FileNotFoundError(f"Model file not found: {model_file}")
            
            self.logger.info(f"BitNet model found at {model_file}")
            
            # Test model loading
            test_result = await self._run_bitnet_inference(
                "Hello, this is a test.", max_tokens=10
            )
            
            if test_result:
                self.logger.info("BitNet model initialized successfully")
            else:
                raise Exception("Failed to initialize BitNet model")
                
        except Exception as e:
            self.logger.error(f"BitNet model initialization failed: {e}")
            raise
    
    async def _run_bitnet_inference(
        self, 
        prompt: str, 
        max_tokens: int = 2048,
        temperature: float = 0.7
    ) -> Optional[str]:
        """Run inference using BitNet."""
        try:
            model_file = Path(self.model_path) / "ggml-model-i2_s.gguf"
            
            # Prepare BitNet command
            cmd = [
                "python3",
                "/app/BitNet/run_inference.py",
                "-m", str(model_file),
                "-p", prompt,
                "-n", str(max_tokens),
                "-temp", str(temperature),
                "-t", str(self.config.BITNET_THREADS)
            ]
            
            # Run inference
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd="/app/BitNet"
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                response = stdout.decode().strip()
                # Extract the generated text (BitNet outputs include prompt)
                if prompt in response:
                    generated_text = response.split(prompt, 1)[1].strip()
                else:
                    generated_text = response
                
                return generated_text
            else:
                self.logger.error(f"BitNet inference failed: {stderr.decode()}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error running BitNet inference: {e}")
            return None
    
    async def process_request(self, request: BitNetInferenceRequest) -> BitNetResponse:
        """Process an inference request."""
        start_time = datetime.now()
        chain_of_thought = []
        
        try:
            # Add role-specific system prompt
            system_prompt = self.role_prompts.get(self.agent_role, "")
            
            # Enable Chain of Thought if requested
            if request.enable_cot:
                cot_prompt = f"{system_prompt}\n\nThink step by step about this request:\n{request.prompt}\n\nLet me think through this:"
                chain_of_thought.append("Initiating Chain of Thought reasoning")
            else:
                cot_prompt = f"{system_prompt}\n\n{request.prompt}"
            
            # Run inference
            response_text = await self._run_bitnet_inference(
                cot_prompt,
                max_tokens=request.max_tokens,
                temperature=request.temperature
            )
            
            if not response_text:
                raise Exception("BitNet inference returned empty response")
            
            # Extract chain of thought if present
            if request.enable_cot and "Let me think through this:" in response_text:
                parts = response_text.split("Let me think through this:", 1)
                if len(parts) > 1:
                    chain_of_thought.append(parts[1].strip())
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            # Count tokens (approximate)
            tokens_generated = len(response_text.split())
            
            # Log the interaction
            self.logger.info(
                f"Processed request - Role: {self.agent_role}, "
                f"Tokens: {tokens_generated}, Time: {processing_time:.2f}s"
            )
            
            return BitNetResponse(
                content=response_text,
                tokens_generated=tokens_generated,
                processing_time=processing_time,
                agent_id=self.agent_id,
                agent_role=self.agent_role,
                chain_of_thought=chain_of_thought
            )
            
        except Exception as e:
            self.logger.error(f"Error processing request: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    async def _register_with_orchestrator(self):
        """Register this agent with the orchestrator."""
        try:
            agent_info = {
                "agent_id": self.agent_id,
                "agent_role": self.agent_role,
                "status": "active",
                "capabilities": [self.agent_role],
                "endpoint": f"http://bitnet-{self.agent_role}:9000",
                "last_heartbeat": datetime.now().isoformat()
            }
            
            await self.redis_client.hset(
                "bitnet_agents",
                self.agent_id,
                json.dumps(agent_info)
            )
            
            self.logger.info("Registered with orchestrator")
            
        except Exception as e:
            self.logger.error(f"Failed to register with orchestrator: {e}")
    
    async def send_heartbeat(self):
        """Send heartbeat to orchestrator."""
        try:
            agent_info = await self.redis_client.hget("bitnet_agents", self.agent_id)
            if agent_info:
                info = json.loads(agent_info)
                info["last_heartbeat"] = datetime.now().isoformat()
                await self.redis_client.hset(
                    "bitnet_agents",
                    self.agent_id,
                    json.dumps(info)
                )
        except Exception as e:
            self.logger.error(f"Failed to send heartbeat: {e}")
    
    async def cleanup(self):
        """Cleanup resources."""
        try:
            if self.redis_client:
                await self.redis_client.close()
            self.logger.info("Agent cleanup completed")
        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")


# FastAPI app for the agent
app = FastAPI(title=f"BitNet Agent", version="1.0.0")

# Global agent instance
agent: Optional[BitNetAgent] = None


@app.on_event("startup")
async def startup_event():
    """Initialize the agent on startup."""
    global agent
    
    agent_id = os.getenv("AGENT_ID", "1")
    agent_role = os.getenv("AGENT_ROLE", "analyzer")
    model_path = os.getenv("BITNET_MODEL_PATH", "/app/models/BitNet-b1.58-2B-4T")
    
    agent = BitNetAgent(agent_id, agent_role, model_path)
    await agent.initialize()


@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown."""
    global agent
    if agent:
        await agent.cleanup()


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "agent_id": agent.agent_id if agent else "unknown"}


@app.post("/inference")
async def inference_endpoint(request: BitNetInferenceRequest):
    """Main inference endpoint."""
    if not agent:
        raise HTTPException(status_code=503, detail="Agent not initialized")
    
    response = await agent.process_request(request)
    return response


@app.get("/status")
async def status_endpoint():
    """Get agent status."""
    if not agent:
        raise HTTPException(status_code=503, detail="Agent not initialized")
    
    return {
        "agent_id": agent.agent_id,
        "agent_role": agent.agent_role,
        "status": "active",
        "model_path": agent.model_path
    }


if __name__ == "__main__":
    # Run the agent server
    port = int(os.getenv("AGENT_PORT", "9000"))
    uvicorn.run(app, host="0.0.0.0", port=port)
