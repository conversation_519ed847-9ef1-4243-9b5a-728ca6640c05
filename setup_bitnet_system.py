#!/usr/bin/env python3
"""Setup script for BitNet Multi-Agent System."""

import os
import sys
import subprocess
import shutil
import asyncio
from pathlib import Path
import argparse
import logging
from typing import List, Dict, Any

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class BitNetSystemSetup:
    """Setup manager for BitNet Multi-Agent System."""
    
    def __init__(self, base_path: Path = None):
        """Initialize setup manager."""
        self.base_path = base_path or Path.cwd()
        self.models_path = self.base_path / "models"
        self.logs_path = self.base_path / "logs"
        self.memory_path = self.base_path / "memory_logs"
        
        # Required directories
        self.required_dirs = [
            "models",
            "logs", 
            "memory_logs",
            "docker/bitnet",
            "orchestrator",
            "agents",
            "config"
        ]
        
        # Required Python packages
        self.required_packages = [
            "fastapi>=0.104.1",
            "uvicorn>=0.24.0",
            "aiohttp>=3.9.1",
            "redis>=5.0.1",
            "python-dotenv>=1.0.1",
            "pydantic>=2.6.4",
            "transformers>=4.36.2",
            "torch>=2.1.2",
            "langchain>=0.2.16",
            "langchain-community>=0.2.17",
            "tiktoken>=0.6.0",
            "huggingface-hub>=0.19.4"
        ]
    
    def check_system_requirements(self) -> List[str]:
        """Check system requirements."""
        issues = []
        
        # Check Python version
        if sys.version_info < (3, 9):
            issues.append("Python 3.9 or higher is required")
        
        # Check for required system tools
        required_tools = ["git", "cmake", "clang"]
        for tool in required_tools:
            if not shutil.which(tool):
                issues.append(f"Required tool not found: {tool}")
        
        # Check for Docker (optional but recommended)
        if not shutil.which("docker"):
            logger.warning("Docker not found - container deployment will not be available")
        
        return issues
    
    def create_directories(self):
        """Create required directories."""
        logger.info("Creating required directories...")
        
        for dir_name in self.required_dirs:
            dir_path = self.base_path / dir_name
            dir_path.mkdir(parents=True, exist_ok=True)
            logger.info(f"Created directory: {dir_path}")
    
    def install_python_dependencies(self):
        """Install required Python packages."""
        logger.info("Installing Python dependencies...")
        
        try:
            # Create requirements.txt if it doesn't exist
            requirements_file = self.base_path / "requirements.txt"
            if not requirements_file.exists():
                with open(requirements_file, 'w') as f:
                    f.write('\n'.join(self.required_packages))
                logger.info("Created requirements.txt")
            
            # Install packages
            subprocess.run([
                sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
            ], check=True)
            
            logger.info("Python dependencies installed successfully")
            
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to install Python dependencies: {e}")
            raise
    
    def setup_bitnet_repository(self):
        """Clone and setup BitNet repository."""
        logger.info("Setting up BitNet repository...")
        
        bitnet_path = self.base_path / "BitNet"
        
        if not bitnet_path.exists():
            try:
                # Clone BitNet repository
                subprocess.run([
                    "git", "clone", "--recursive", 
                    "https://github.com/microsoft/BitNet.git",
                    str(bitnet_path)
                ], check=True)
                
                logger.info("BitNet repository cloned successfully")
                
                # Install BitNet requirements
                bitnet_requirements = bitnet_path / "requirements.txt"
                if bitnet_requirements.exists():
                    subprocess.run([
                        sys.executable, "-m", "pip", "install", "-r", str(bitnet_requirements)
                    ], check=True)
                    logger.info("BitNet requirements installed")
                
            except subprocess.CalledProcessError as e:
                logger.error(f"Failed to setup BitNet repository: {e}")
                raise
        else:
            logger.info("BitNet repository already exists")
    
    def download_bitnet_model(self, model_name: str = "microsoft/BitNet-b1.58-2B-4T"):
        """Download BitNet model."""
        logger.info(f"Downloading BitNet model: {model_name}")
        
        try:
            # Create model directory
            model_dir = self.models_path / "BitNet-b1.58-2B-4T"
            model_dir.mkdir(parents=True, exist_ok=True)
            
            # Download model using huggingface-cli
            subprocess.run([
                "huggingface-cli", "download", 
                "microsoft/BitNet-b1.58-2B-4T-gguf",
                "--local-dir", str(model_dir)
            ], check=True)
            
            logger.info("BitNet model downloaded successfully")
            
            # Setup model for inference
            bitnet_path = self.base_path / "BitNet"
            if bitnet_path.exists():
                subprocess.run([
                    sys.executable, str(bitnet_path / "setup_env.py"),
                    "-md", str(model_dir),
                    "-q", "i2_s"
                ], cwd=str(bitnet_path), check=True)
                
                logger.info("BitNet model setup completed")
            
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to download/setup BitNet model: {e}")
            logger.info("You can manually download the model later using:")
            logger.info(f"huggingface-cli download microsoft/BitNet-b1.58-2B-4T-gguf --local-dir {model_dir}")
    
    def create_environment_file(self):
        """Create .env file from template."""
        logger.info("Creating environment configuration...")
        
        env_file = self.base_path / ".env"
        env_example = self.base_path / ".env.example"
        
        if not env_file.exists() and env_example.exists():
            shutil.copy(env_example, env_file)
            logger.info("Created .env file from template")
            logger.info("Please edit .env file to configure your system")
        elif env_file.exists():
            logger.info(".env file already exists")
        else:
            logger.warning(".env.example not found - creating basic .env file")
            
            basic_env = """# BitNet Multi-Agent System Configuration
BITNET_AGENT_COUNT=4
BITNET_MODEL_PATH=./models/BitNet-b1.58-2B-4T
REASONING_MODEL_URL=http://localhost:8001/v1
REASONING_MODEL_NAME=llama-3.1-8b-instruct
DOCKER_ENABLED=true
ENABLE_CHAIN_OF_THOUGHT=true
ENABLE_MIXTURE_OF_EXPERTS=true
"""
            with open(env_file, 'w') as f:
                f.write(basic_env)
            
            logger.info("Created basic .env file")
    
    def build_docker_images(self):
        """Build Docker images."""
        logger.info("Building Docker images...")
        
        try:
            # Check if Docker is available
            if not shutil.which("docker"):
                logger.warning("Docker not available - skipping image build")
                return
            
            # Build BitNet agent image
            docker_dir = self.base_path / "docker"
            if docker_dir.exists():
                subprocess.run([
                    "docker", "build", 
                    "-t", "bitnet-agent:latest",
                    "-f", str(docker_dir / "bitnet" / "Dockerfile"),
                    str(docker_dir / "bitnet")
                ], check=True)
                
                logger.info("BitNet agent Docker image built successfully")
            
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to build Docker images: {e}")
            logger.info("You can build images manually later using docker-compose")
    
    def validate_setup(self) -> Dict[str, Any]:
        """Validate the setup."""
        logger.info("Validating setup...")
        
        validation_results = {
            "directories": {},
            "files": {},
            "models": {},
            "dependencies": {}
        }
        
        # Check directories
        for dir_name in self.required_dirs:
            dir_path = self.base_path / dir_name
            validation_results["directories"][dir_name] = dir_path.exists()
        
        # Check important files
        important_files = [
            ".env",
            "requirements.txt",
            "docker/docker-compose.yml",
            "agents/bitnet_agent.py",
            "orchestrator/multi_agent_orchestrator.py"
        ]
        
        for file_name in important_files:
            file_path = self.base_path / file_name
            validation_results["files"][file_name] = file_path.exists()
        
        # Check model
        model_file = self.models_path / "BitNet-b1.58-2B-4T" / "ggml-model-i2_s.gguf"
        validation_results["models"]["bitnet_model"] = model_file.exists()
        
        # Check BitNet repository
        bitnet_path = self.base_path / "BitNet"
        validation_results["dependencies"]["bitnet_repo"] = bitnet_path.exists()
        
        return validation_results
    
    def print_validation_results(self, results: Dict[str, Any]):
        """Print validation results."""
        logger.info("Setup Validation Results:")
        logger.info("=" * 50)
        
        for category, items in results.items():
            logger.info(f"\n{category.upper()}:")
            for item, status in items.items():
                status_str = "✓" if status else "✗"
                logger.info(f"  {status_str} {item}")
        
        # Check if setup is complete
        all_good = all(
            all(items.values()) for items in results.values()
        )
        
        if all_good:
            logger.info("\n🎉 Setup completed successfully!")
            logger.info("You can now run the system with: python run_multi_agent_system.py")
        else:
            logger.warning("\n⚠️  Setup incomplete - please address the missing items above")
    
    def run_setup(self, skip_model_download: bool = False, skip_docker: bool = False):
        """Run the complete setup process."""
        logger.info("Starting BitNet Multi-Agent System setup...")
        
        try:
            # Check system requirements
            issues = self.check_system_requirements()
            if issues:
                logger.error("System requirements not met:")
                for issue in issues:
                    logger.error(f"  - {issue}")
                return False
            
            # Create directories
            self.create_directories()
            
            # Install Python dependencies
            self.install_python_dependencies()
            
            # Setup BitNet repository
            self.setup_bitnet_repository()
            
            # Download model (optional)
            if not skip_model_download:
                try:
                    self.download_bitnet_model()
                except Exception as e:
                    logger.warning(f"Model download failed: {e}")
                    logger.info("You can download the model manually later")
            
            # Create environment file
            self.create_environment_file()
            
            # Build Docker images (optional)
            if not skip_docker:
                try:
                    self.build_docker_images()
                except Exception as e:
                    logger.warning(f"Docker build failed: {e}")
                    logger.info("You can build Docker images manually later")
            
            # Validate setup
            results = self.validate_setup()
            self.print_validation_results(results)
            
            return True
            
        except Exception as e:
            logger.error(f"Setup failed: {e}")
            return False


def main():
    """Main setup function."""
    parser = argparse.ArgumentParser(description="Setup BitNet Multi-Agent System")
    parser.add_argument("--skip-model", action="store_true", help="Skip model download")
    parser.add_argument("--skip-docker", action="store_true", help="Skip Docker setup")
    parser.add_argument("--base-path", type=str, help="Base path for installation")
    
    args = parser.parse_args()
    
    base_path = Path(args.base_path) if args.base_path else Path.cwd()
    setup = BitNetSystemSetup(base_path)
    
    success = setup.run_setup(
        skip_model_download=args.skip_model,
        skip_docker=args.skip_docker
    )
    
    if success:
        logger.info("Setup completed successfully!")
        sys.exit(0)
    else:
        logger.error("Setup failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
