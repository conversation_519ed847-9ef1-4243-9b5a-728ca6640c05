version: '3.8'

services:
  # Redis for inter-agent communication
  redis:
    image: redis:7-alpine
    container_name: bitnet-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - bitnet-network
    restart: unless-stopped

  # BitNet Agent 1 - Analyzer
  bitnet-analyzer:
    build:
      context: ./bitnet
      dockerfile: Dockerfile
    container_name: bitnet-analyzer
    environment:
      - AGENT_ROLE=analyzer
      - AGENT_ID=1
      - REDIS_URL=redis://redis:6379
      - BITNET_MODEL_PATH=/app/models/BitNet-b1.58-2B-4T
    ports:
      - "9001:9000"
    volumes:
      - ../models:/app/models:ro
      - ../logs:/app/logs
      - ../memory_logs:/app/memory
    networks:
      - bitnet-network
    depends_on:
      - redis
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 4G

  # BitNet Agent 2 - Coder
  bitnet-coder:
    build:
      context: ./bitnet
      dockerfile: Dockerfile
    container_name: bitnet-coder
    environment:
      - AGENT_ROLE=coder
      - AGENT_ID=2
      - REDIS_URL=redis://redis:6379
      - BITNET_MODEL_PATH=/app/models/BitNet-b1.58-2B-4T
    ports:
      - "9002:9000"
    volumes:
      - ../models:/app/models:ro
      - ../logs:/app/logs
      - ../memory_logs:/app/memory
    networks:
      - bitnet-network
    depends_on:
      - redis
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 4G

  # BitNet Agent 3 - Reviewer
  bitnet-reviewer:
    build:
      context: ./bitnet
      dockerfile: Dockerfile
    container_name: bitnet-reviewer
    environment:
      - AGENT_ROLE=reviewer
      - AGENT_ID=3
      - REDIS_URL=redis://redis:6379
      - BITNET_MODEL_PATH=/app/models/BitNet-b1.58-2B-4T
    ports:
      - "9003:9000"
    volumes:
      - ../models:/app/models:ro
      - ../logs:/app/logs
      - ../memory_logs:/app/memory
    networks:
      - bitnet-network
    depends_on:
      - redis
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 4G

  # BitNet Agent 4 - Researcher
  bitnet-researcher:
    build:
      context: ./bitnet
      dockerfile: Dockerfile
    container_name: bitnet-researcher
    environment:
      - AGENT_ROLE=researcher
      - AGENT_ID=4
      - REDIS_URL=redis://redis:6379
      - BITNET_MODEL_PATH=/app/models/BitNet-b1.58-2B-4T
    ports:
      - "9004:9000"
    volumes:
      - ../models:/app/models:ro
      - ../logs:/app/logs
      - ../memory_logs:/app/memory
    networks:
      - bitnet-network
    depends_on:
      - redis
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 4G

  # Orchestrator
  orchestrator:
    build:
      context: ..
      dockerfile: docker/orchestrator/Dockerfile
    container_name: bitnet-orchestrator
    environment:
      - REDIS_URL=redis://redis:6379
      - REASONING_MODEL_URL=${REASONING_MODEL_URL}
      - REASONING_MODEL_NAME=${REASONING_MODEL_NAME}
    ports:
      - "9200:9200"
    volumes:
      - ../logs:/app/logs
      - ../memory_logs:/app/memory
      - ../.env:/app/.env
    networks:
      - bitnet-network
    depends_on:
      - redis
      - bitnet-analyzer
      - bitnet-coder
      - bitnet-reviewer
      - bitnet-researcher
    restart: unless-stopped

networks:
  bitnet-network:
    driver: bridge

volumes:
  redis_data:
