#!/usr/bin/env python3
"""BitNet Agent Server for Docker deployment."""

import os
import sys
import asyncio
import logging
from pathlib import Path

# Add app directory to path
sys.path.append('/app')

# Import the BitNet agent
from agents.bitnet_agent import app

if __name__ == "__main__":
    import uvicorn
    
    # Get configuration from environment
    port = int(os.getenv("AGENT_PORT", "9000"))
    host = "0.0.0.0"
    
    # Setup logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    logger.info(f"Starting BitNet Agent Server on {host}:{port}")
    logger.info(f"Agent ID: {os.getenv('AGENT_ID', 'unknown')}")
    logger.info(f"Agent Role: {os.getenv('AGENT_ROLE', 'unknown')}")
    
    # Run the server
    uvicorn.run(app, host=host, port=port, log_level="info")
