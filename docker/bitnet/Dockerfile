# BitNet Agent Docker Container
FROM ubuntu:22.04

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    python3-dev \
    cmake \
    build-essential \
    git \
    wget \
    curl \
    clang-18 \
    libomp-dev \
    && rm -rf /var/lib/apt/lists/*

# Create symbolic links for clang
RUN ln -s /usr/bin/clang-18 /usr/bin/clang && \
    ln -s /usr/bin/clang++-18 /usr/bin/clang++

# Set working directory
WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip3 install --no-cache-dir -r requirements.txt

# Clone and build BitNet
RUN git clone --recursive https://github.com/microsoft/BitNet.git /app/BitNet
WORKDIR /app/BitNet

# Install BitNet requirements
RUN pip3 install --no-cache-dir -r requirements.txt

# Copy application code from parent directory
COPY ../../agents /app/agents
COPY ../../config /app/config
COPY ../../orchestrator /app/orchestrator
COPY bitnet_agent_server.py /app/

# Create necessary directories
RUN mkdir -p /app/models /app/logs /app/memory

# Set permissions
RUN chmod +x /app/*.py

# Expose port for agent communication
EXPOSE 9000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:9000/health || exit 1

# Default command
CMD ["python3", "bitnet_agent_server.py"]
