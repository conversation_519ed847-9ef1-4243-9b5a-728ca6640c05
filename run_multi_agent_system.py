#!/usr/bin/env python3
"""Main entry point for BitNet Multi-Agent System."""

import os
import sys
import asyncio
import argparse
import logging
import signal
from pathlib import Path
from typing import Optional, Dict, Any
import subprocess
import time

# Add current directory to path
sys.path.append(str(Path(__file__).parent))

from config.bitnet_config import BitNetConfig
from orchestrator.multi_agent_orchestrator import MultiAgentOrchestrator


class BitNetMultiAgentSystem:
    """Main system controller for BitNet Multi-Agent System."""
    
    def __init__(self, config_path: Optional[str] = None):
        """Initialize the system."""
        # Load configuration
        if config_path:
            os.environ["DOTENV_PATH"] = config_path
        
        self.config = BitNetConfig()
        
        # Setup logging
        self.logger = self._setup_logging()
        
        # System components
        self.orchestrator: Optional[MultiAgentOrchestrator] = None
        self.docker_processes: Dict[str, subprocess.Popen] = {}
        self.running = False
        
        # Validate configuration
        self._validate_configuration()
        
        self.logger.info("BitNet Multi-Agent System initialized")
    
    def _setup_logging(self) -> logging.Logger:
        """Setup system logging."""
        # Ensure log directory exists
        self.config.ensure_directories()
        
        # Configure logging
        log_level = getattr(logging, self.config.LOG_LEVEL.upper(), logging.INFO)
        
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(Path(self.config.LOG_FOLDER) / "system.log"),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        return logging.getLogger("bitnet_system")
    
    def _validate_configuration(self):
        """Validate system configuration."""
        errors = self.config.validate_config()
        
        if errors:
            self.logger.error("Configuration validation failed:")
            for error in errors:
                self.logger.error(f"  - {error}")
            raise ValueError("Invalid configuration")
        
        self.logger.info("Configuration validated successfully")
    
    async def start_system(self, use_docker: bool = None):
        """Start the multi-agent system."""
        try:
            self.logger.info("Starting BitNet Multi-Agent System...")
            
            # Determine deployment mode
            if use_docker is None:
                use_docker = self.config.DOCKER_ENABLED
            
            if use_docker:
                await self._start_docker_deployment()
            else:
                await self._start_local_deployment()
            
            # Start orchestrator
            await self._start_orchestrator()
            
            self.running = True
            self.logger.info("System started successfully!")
            
            # Print system status
            await self._print_system_status()
            
        except Exception as e:
            self.logger.error(f"Failed to start system: {e}")
            await self.stop_system()
            raise
    
    async def _start_docker_deployment(self):
        """Start system using Docker containers."""
        self.logger.info("Starting Docker deployment...")
        
        try:
            # Check if Docker is available
            result = subprocess.run(["docker", "--version"], capture_output=True, text=True)
            if result.returncode != 0:
                raise Exception("Docker is not available")
            
            # Start Docker Compose
            docker_compose_path = Path("docker/docker-compose.yml")
            if not docker_compose_path.exists():
                raise Exception("Docker Compose file not found")
            
            # Build and start containers
            subprocess.run([
                "docker-compose", "-f", str(docker_compose_path), "up", "-d", "--build"
            ], check=True)
            
            self.logger.info("Docker containers started")
            
            # Wait for containers to be ready
            await self._wait_for_containers()
            
        except subprocess.CalledProcessError as e:
            self.logger.error(f"Docker deployment failed: {e}")
            raise
        except Exception as e:
            self.logger.error(f"Error in Docker deployment: {e}")
            raise
    
    async def _start_local_deployment(self):
        """Start system using local processes."""
        self.logger.info("Starting local deployment...")
        
        try:
            # Start Redis if not running
            await self._ensure_redis_running()
            
            # Start BitNet agents
            await self._start_local_agents()
            
            self.logger.info("Local deployment started")
            
        except Exception as e:
            self.logger.error(f"Local deployment failed: {e}")
            raise
    
    async def _ensure_redis_running(self):
        """Ensure Redis is running locally."""
        try:
            # Try to connect to Redis
            import redis
            client = redis.Redis.from_url(self.config.REDIS_URL)
            client.ping()
            self.logger.info("Redis is already running")
            
        except Exception:
            self.logger.info("Starting Redis server...")
            
            # Try to start Redis
            try:
                redis_process = subprocess.Popen([
                    "redis-server", "--daemonize", "yes"
                ])
                
                # Wait a bit for Redis to start
                await asyncio.sleep(2)
                
                # Test connection again
                client = redis.Redis.from_url(self.config.REDIS_URL)
                client.ping()
                
                self.logger.info("Redis started successfully")
                
            except Exception as e:
                self.logger.error(f"Failed to start Redis: {e}")
                self.logger.info("Please ensure Redis is installed and running")
                raise
    
    async def _start_local_agents(self):
        """Start BitNet agents as local processes."""
        for i, role in enumerate(self.config.BITNET_AGENT_ROLES[:self.config.BITNET_AGENT_COUNT]):
            try:
                agent_id = str(i + 1)
                port = self.config.BITNET_BASE_PORT + i + 1
                
                # Set environment variables for the agent
                env = os.environ.copy()
                env.update({
                    "AGENT_ID": agent_id,
                    "AGENT_ROLE": role,
                    "AGENT_PORT": str(port),
                    "BITNET_MODEL_PATH": self.config.BITNET_MODEL_PATH,
                    "REDIS_URL": self.config.REDIS_URL
                })
                
                # Start agent process
                process = subprocess.Popen([
                    sys.executable, "agents/bitnet_agent.py"
                ], env=env)
                
                self.docker_processes[f"agent_{agent_id}"] = process
                
                self.logger.info(f"Started BitNet agent {agent_id} ({role}) on port {port}")
                
                # Small delay between agent starts
                await asyncio.sleep(1)
                
            except Exception as e:
                self.logger.error(f"Failed to start agent {agent_id}: {e}")
                raise
    
    async def _wait_for_containers(self):
        """Wait for Docker containers to be ready."""
        self.logger.info("Waiting for containers to be ready...")
        
        max_wait = 60  # Maximum wait time in seconds
        wait_time = 0
        
        while wait_time < max_wait:
            try:
                # Check if Redis is accessible
                import redis
                client = redis.Redis.from_url(self.config.REDIS_URL)
                client.ping()
                
                # Check if agents are registered
                agents_data = client.hgetall("bitnet_agents")
                if len(agents_data) >= self.config.BITNET_AGENT_COUNT:
                    self.logger.info("All containers are ready")
                    return
                
            except Exception:
                pass
            
            await asyncio.sleep(2)
            wait_time += 2
        
        self.logger.warning("Containers may not be fully ready, continuing anyway...")
    
    async def _start_orchestrator(self):
        """Start the orchestrator."""
        try:
            self.orchestrator = MultiAgentOrchestrator()
            await self.orchestrator.initialize()
            
            self.logger.info("Orchestrator started successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to start orchestrator: {e}")
            raise
    
    async def _print_system_status(self):
        """Print current system status."""
        if self.orchestrator:
            status = await self.orchestrator.get_system_status()
            
            self.logger.info("=" * 60)
            self.logger.info("SYSTEM STATUS")
            self.logger.info("=" * 60)
            self.logger.info(f"Overall Status: {status.get('status', 'unknown')}")
            self.logger.info(f"Active Agents: {status.get('agents', {}).get('active', 0)}")
            self.logger.info(f"Total Tasks: {status.get('tasks', {}).get('total', 0)}")
            self.logger.info(f"Reasoning Agent: {status.get('reasoning_agent', {}).get('status', 'unknown')}")
            self.logger.info("=" * 60)
    
    async def stop_system(self):
        """Stop the multi-agent system."""
        self.logger.info("Stopping BitNet Multi-Agent System...")
        
        try:
            self.running = False
            
            # Stop orchestrator
            if self.orchestrator:
                await self.orchestrator.cleanup()
                self.orchestrator = None
            
            # Stop local processes
            for name, process in self.docker_processes.items():
                try:
                    process.terminate()
                    process.wait(timeout=5)
                    self.logger.info(f"Stopped process: {name}")
                except subprocess.TimeoutExpired:
                    process.kill()
                    self.logger.warning(f"Force killed process: {name}")
                except Exception as e:
                    self.logger.error(f"Error stopping process {name}: {e}")
            
            self.docker_processes.clear()
            
            # Stop Docker containers if using Docker
            if self.config.DOCKER_ENABLED:
                try:
                    docker_compose_path = Path("docker/docker-compose.yml")
                    if docker_compose_path.exists():
                        subprocess.run([
                            "docker-compose", "-f", str(docker_compose_path), "down"
                        ], check=True)
                        self.logger.info("Docker containers stopped")
                except Exception as e:
                    self.logger.error(f"Error stopping Docker containers: {e}")
            
            self.logger.info("System stopped successfully")
            
        except Exception as e:
            self.logger.error(f"Error during system shutdown: {e}")
    
    async def run_interactive_mode(self):
        """Run the system in interactive mode."""
        self.logger.info("Starting interactive mode...")
        self.logger.info("Type 'help' for available commands, 'quit' to exit")
        
        while self.running:
            try:
                command = input("\nbitnet> ").strip().lower()
                
                if command == "quit" or command == "exit":
                    break
                elif command == "help":
                    self._print_help()
                elif command == "status":
                    await self._print_system_status()
                elif command.startswith("task "):
                    task_description = command[5:]
                    await self._run_interactive_task(task_description)
                else:
                    print("Unknown command. Type 'help' for available commands.")
                    
            except KeyboardInterrupt:
                break
            except EOFError:
                break
            except Exception as e:
                self.logger.error(f"Error in interactive mode: {e}")
    
    def _print_help(self):
        """Print help information."""
        help_text = """
Available commands:
  help                 - Show this help message
  status              - Show system status
  task <description>  - Run a task with the given description
  quit/exit           - Stop the system and exit

Examples:
  task Analyze the performance of sorting algorithms
  task Generate a Python function to calculate fibonacci numbers
  task Review this code for security vulnerabilities
"""
        print(help_text)
    
    async def _run_interactive_task(self, description: str):
        """Run a task interactively."""
        try:
            if not self.orchestrator:
                print("Error: Orchestrator not available")
                return
            
            from orchestrator.multi_agent_orchestrator import MultiAgentTask
            
            task = MultiAgentTask(
                task_description=description,
                task_type="general"
            )
            
            print(f"Processing task: {description}")
            result = await self.orchestrator.process_task(task)
            
            print(f"\nTask completed (Confidence: {result.confidence_score:.2f})")
            print(f"Result:\n{result.final_result}")
            
        except Exception as e:
            print(f"Error processing task: {e}")


async def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="BitNet Multi-Agent System")
    parser.add_argument("--config", type=str, help="Path to configuration file")
    parser.add_argument("--no-docker", action="store_true", help="Disable Docker deployment")
    parser.add_argument("--interactive", action="store_true", help="Run in interactive mode")
    
    args = parser.parse_args()
    
    # Initialize system
    system = BitNetMultiAgentSystem(args.config)
    
    # Setup signal handlers
    def signal_handler(signum, frame):
        print("\nReceived interrupt signal, shutting down...")
        asyncio.create_task(system.stop_system())
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # Start system
        await system.start_system(use_docker=not args.no_docker)
        
        if args.interactive:
            # Run interactive mode
            await system.run_interactive_mode()
        else:
            # Keep system running
            print("System is running. Press Ctrl+C to stop.")
            while system.running:
                await asyncio.sleep(1)
    
    except KeyboardInterrupt:
        print("\nShutdown requested...")
    except Exception as e:
        print(f"System error: {e}")
    finally:
        await system.stop_system()


if __name__ == "__main__":
    asyncio.run(main())
