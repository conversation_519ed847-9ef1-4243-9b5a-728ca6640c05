#!/usr/bin/env python3
"""Hierarchical Workflow System: Local Folder → BitNet Agents → vLLM Reasoning → plan.json"""

import asyncio
import aiohttp
import json
import os
import time
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


@dataclass
class ConversationEntry:
    """Entry in the conversation log."""
    timestamp: datetime
    stage: str  # "user_input", "bitnet_processing", "vllm_reasoning", "plan_generation"
    content: str
    metadata: Dict[str, Any]


@dataclass
class BitNetAgentResult:
    """Result from a BitNet agent."""
    agent_role: str
    content: str
    summary: str  # Condensed version for vLLM
    tokens_processed: int
    processing_time: float


@dataclass
class PlanItem:
    """Individual item in the plan.json."""
    file_path: str
    change_type: str  # "create", "modify", "delete"
    description: str
    priority: int
    estimated_effort: str


class HierarchicalWorkflowSystem:
    """Implements the Local Folder → BitNet → vLLM → plan.json workflow."""

    def __init__(self, vllm_url: str = None):
        """Initialize the hierarchical workflow system."""
        # Use environment variable or fallback to default
        if vllm_url is None:
            vllm_url = os.getenv('REASONING_MODEL_URL', 'http://localhost:8001/v1')

        self.vllm_url = vllm_url
        self.conversation_log: List[ConversationEntry] = []

        # Log the URL being used
        print(f"🔧 Using vLLM server: {self.vllm_url}")
        
        # BitNet agent specializations for code workflow
        self.bitnet_agents = {
            "file_analyzer": {
                "prompt": "You are a file analysis expert. Analyze code files, understand structure, dependencies, and provide detailed technical summaries.",
                "focus": "File structure, imports, classes, functions, dependencies"
            },
            "code_generator": {
                "prompt": "You are a code generation expert. Generate clean, efficient code solutions based on requirements and existing codebase patterns.",
                "focus": "Code implementation, patterns, best practices"
            },
            "context_summarizer": {
                "prompt": "You are a context summarization expert. Create concise summaries of large codebases while preserving critical technical details.",
                "focus": "Key insights, architectural patterns, critical components"
            },
            "change_detector": {
                "prompt": "You are a change detection expert. Identify what needs to be modified, added, or removed based on requirements.",
                "focus": "Required changes, impact analysis, modification scope"
            }
        }
        
        # Setup logging
        self.logger = self._setup_logging()
        
        # Ensure directories exist
        self._ensure_directories()
    
    def _setup_logging(self) -> logging.Logger:
        """Setup logging for the workflow system."""
        logger = logging.getLogger("hierarchical_workflow")
        logger.setLevel(logging.INFO)
        
        # Create logs directory
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # File handler
        file_handler = logging.FileHandler(log_dir / "hierarchical_workflow.log")
        file_handler.setLevel(logging.INFO)
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # Formatter
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        return logger
    
    def _ensure_directories(self):
        """Ensure all required directories exist."""
        directories = [
            "conversation_logs",
            "plans", 
            "memory_logs",
            "logs"
        ]
        
        for directory in directories:
            Path(directory).mkdir(exist_ok=True)
    
    def _log_conversation(self, stage: str, content: str, metadata: Dict[str, Any] = None):
        """Log a conversation entry."""
        entry = ConversationEntry(
            timestamp=datetime.now(),
            stage=stage,
            content=content,
            metadata=metadata or {}
        )
        
        self.conversation_log.append(entry)
        self.logger.info(f"[{stage}] {content[:100]}...")
    
    async def analyze_local_folder(self, folder_path: str) -> Dict[str, Any]:
        """Analyze the local folder and extract codebase information."""
        self._log_conversation("folder_analysis", f"Analyzing folder: {folder_path}")
        
        folder_info = {
            "path": folder_path,
            "files": [],
            "structure": {},
            "total_tokens": 0,
            "file_contents": {}
        }
        
        try:
            # Walk through the directory
            for root, dirs, files in os.walk(folder_path):
                # Skip hidden directories and common ignore patterns
                dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['node_modules', '__pycache__', 'venv']]
                
                for file in files:
                    if file.startswith('.'):
                        continue
                    
                    file_path = os.path.join(root, file)
                    relative_path = os.path.relpath(file_path, folder_path)
                    
                    # Only process text files
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                            
                        # Estimate tokens (rough approximation)
                        tokens = len(content.split())
                        
                        folder_info["files"].append({
                            "path": relative_path,
                            "size": len(content),
                            "tokens": tokens,
                            "extension": os.path.splitext(file)[1]
                        })
                        
                        folder_info["file_contents"][relative_path] = content
                        folder_info["total_tokens"] += tokens
                        
                    except (UnicodeDecodeError, PermissionError):
                        # Skip binary files or files we can't read
                        continue
            
            self._log_conversation(
                "folder_analysis_complete", 
                f"Analyzed {len(folder_info['files'])} files, ~{folder_info['total_tokens']} tokens",
                {"file_count": len(folder_info['files']), "total_tokens": folder_info['total_tokens']}
            )
            
            return folder_info
            
        except Exception as e:
            self.logger.error(f"Error analyzing folder: {e}")
            raise
    
    async def process_with_bitnet_agents(self, folder_info: Dict[str, Any], user_request: str) -> List[BitNetAgentResult]:
        """Process the codebase with specialized BitNet agents."""
        self._log_conversation("bitnet_processing_start", f"Processing with {len(self.bitnet_agents)} BitNet agents")
        
        results = []
        
        # Prepare context for agents
        context = {
            "user_request": user_request,
            "folder_path": folder_info["path"],
            "file_count": len(folder_info["files"]),
            "total_tokens": folder_info["total_tokens"],
            "file_list": [f["path"] for f in folder_info["files"]],
            "file_contents": folder_info["file_contents"]
        }
        
        # Process with each BitNet agent
        for agent_role, agent_config in self.bitnet_agents.items():
            self.logger.info(f"Processing with {agent_role} agent...")
            
            # Build agent-specific prompt
            agent_prompt = f"""{agent_config['prompt']}

USER REQUEST: {user_request}

CODEBASE CONTEXT:
- Folder: {folder_info['path']}
- Files: {len(folder_info['files'])} files
- Total tokens: ~{folder_info['total_tokens']}

FOCUS AREA: {agent_config['focus']}

FILES TO ANALYZE:
"""
            
            # Add file contents (truncated if too large)
            for file_path, content in folder_info["file_contents"].items():
                agent_prompt += f"\n--- {file_path} ---\n"
                if len(content) > 2000:  # Truncate very large files
                    agent_prompt += content[:2000] + "\n... [truncated] ..."
                else:
                    agent_prompt += content
                agent_prompt += "\n"
            
            agent_prompt += f"""

TASK: Based on your expertise in {agent_config['focus']}, analyze this codebase and provide:
1. Detailed analysis relevant to the user request
2. Specific recommendations for your area of expertise
3. A concise summary (max 500 words) for the reasoning model

Please structure your response as:
DETAILED_ANALYSIS:
[Your detailed analysis here]

RECOMMENDATIONS:
[Your specific recommendations]

SUMMARY_FOR_REASONING:
[Concise summary for the reasoning model]
"""
            
            # Simulate BitNet agent call (replace with actual BitNet when available)
            result = await self._simulate_bitnet_agent(agent_role, agent_prompt)
            results.append(result)
            
            self._log_conversation(
                "bitnet_agent_complete",
                f"{agent_role} completed analysis",
                {"agent": agent_role, "tokens_processed": result.tokens_processed}
            )
        
        return results
    
    async def _simulate_bitnet_agent(self, agent_role: str, prompt: str) -> BitNetAgentResult:
        """Simulate BitNet agent processing (replace with actual BitNet calls)."""
        start_time = time.time()
        
        # For now, use vLLM to simulate BitNet agents
        # In production, this would call actual BitNet models
        try:
            async with aiohttp.ClientSession() as session:
                # Get model name from environment
                model_name = os.getenv('REASONING_MODEL_NAME', 'llama-3.1-8b-instruct')

                # Calculate safe token limit (model max 2048, leave room for prompt)
                prompt_tokens = len(prompt.split()) * 1.3  # Rough estimate with safety margin
                max_completion_tokens = max(200, min(800, int(2048 - prompt_tokens)))

                payload = {
                    "model": model_name,
                    "prompt": prompt,
                    "max_tokens": max_completion_tokens,
                    "temperature": 0.7,
                    "stop": ["Human:", "Assistant:"]
                }

                # Use the known working endpoint
                endpoint = f"{self.vllm_url}/completions"

                async with session.post(
                    endpoint,
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=120)
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        content = result["choices"][0]["text"].strip()
                    else:
                        error_text = await response.text()
                        raise Exception(f"API call failed: HTTP {response.status} from {endpoint}. Error: {error_text}")

                # Extract summary for reasoning
                summary = ""
                if "SUMMARY_FOR_REASONING:" in content:
                    summary = content.split("SUMMARY_FOR_REASONING:")[1].strip()
                else:
                    # Fallback: use first 500 words as summary
                    words = content.split()
                    summary = " ".join(words[:500])

                processing_time = time.time() - start_time

                return BitNetAgentResult(
                    agent_role=agent_role,
                    content=content,
                    summary=summary,
                    tokens_processed=len(prompt.split()),
                    processing_time=processing_time
                )
                        
        except Exception as e:
            self.logger.error(f"Error in {agent_role} agent: {e}")
            processing_time = time.time() - start_time
            
            return BitNetAgentResult(
                agent_role=agent_role,
                content=f"Error in {agent_role}: {str(e)}",
                summary=f"Agent {agent_role} encountered an error",
                tokens_processed=0,
                processing_time=processing_time
            )
    
    async def reason_with_vllm(self, bitnet_results: List[BitNetAgentResult], user_request: str) -> Dict[str, Any]:
        """Use vLLM for high-level reasoning over BitNet summaries."""
        self._log_conversation("vllm_reasoning_start", "Starting vLLM reasoning phase")
        
        # Build reasoning prompt with condensed summaries
        reasoning_prompt = f"""You are a senior software architect and project manager. You need to create a strategic plan based on analysis from specialized agents.

USER REQUEST: {user_request}

AGENT ANALYSIS SUMMARIES:
"""
        
        for result in bitnet_results:
            reasoning_prompt += f"\n--- {result.agent_role.upper()} AGENT SUMMARY ---\n"
            reasoning_prompt += result.summary
            reasoning_prompt += "\n"
        
        reasoning_prompt += """

TASK: Based on these agent summaries, create a strategic implementation plan. Focus on:
1. High-level strategy and approach
2. Key technical decisions
3. Risk assessment
4. Implementation priorities

Provide your reasoning in a structured format that will inform the final plan generation.

REASONING:
[Your strategic reasoning here]

KEY_INSIGHTS:
[Critical insights that will drive the plan]

IMPLEMENTATION_STRATEGY:
[High-level implementation approach]
"""
        
        try:
            async with aiohttp.ClientSession() as session:
                # Get model name from environment
                model_name = os.getenv('REASONING_MODEL_NAME', 'llama-3.1-8b-instruct')

                # Calculate safe token limit (model max 2048, leave room for prompt)
                prompt_tokens = len(reasoning_prompt.split()) * 1.3  # Rough estimate with safety margin
                max_completion_tokens = max(200, min(1000, int(2048 - prompt_tokens)))

                payload = {
                    "model": model_name,
                    "prompt": reasoning_prompt,
                    "max_tokens": max_completion_tokens,
                    "temperature": 0.3,
                    "stop": ["Human:", "Assistant:"]
                }

                # Use the known working endpoint
                endpoint = f"{self.vllm_url}/completions"

                async with session.post(
                    endpoint,
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=120)
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        reasoning_content = result["choices"][0]["text"].strip()
                    else:
                        error_text = await response.text()
                        raise Exception(f"vLLM reasoning failed: HTTP {response.status} from {endpoint}. Error: {error_text}")

                self._log_conversation(
                    "vllm_reasoning_complete",
                    "vLLM reasoning completed",
                    {"reasoning_length": len(reasoning_content)}
                )

                return {
                    "reasoning": reasoning_content,
                    "status": "success"
                }
                        
        except Exception as e:
            self.logger.error(f"Error in vLLM reasoning: {e}")
            return {
                "reasoning": f"Error in reasoning: {str(e)}",
                "status": "error"
            }
    
    async def generate_plan_json(self, reasoning_result: Dict[str, Any], bitnet_results: List[BitNetAgentResult], user_request: str) -> Dict[str, Any]:
        """Generate the final plan.json based on reasoning and agent outputs."""
        self._log_conversation("plan_generation_start", "Generating plan.json")
        
        # Build minimal plan generation prompt to fit token limits
        plan_prompt = f"""Create JSON plan for: {user_request}

{{
  "summary": "Project analysis and recommendations",
  "changes": [
    {{
      "file_path": "main.py",
      "description": "Add error handling and documentation",
      "priority": 1,
      "effort": "medium"
    }}
  ],
  "next_steps": ["Review code structure", "Add tests", "Improve documentation"]
}}"""
        
        try:
            async with aiohttp.ClientSession() as session:
                # Get model name from environment
                model_name = os.getenv('REASONING_MODEL_NAME', '/models/DeepSeek-R1-Distill-Llama-8B-Q6_K.gguf')

                # Calculate safe token limit (model max 2048, leave room for prompt)
                prompt_tokens = len(plan_prompt.split()) * 1.3  # Rough estimate with safety margin
                max_completion_tokens = max(200, min(1200, int(2048 - prompt_tokens)))

                payload = {
                    "model": model_name,
                    "prompt": plan_prompt,
                    "max_tokens": max_completion_tokens,
                    "temperature": 0.2,
                    "stop": ["Human:", "Assistant:"]
                }

                # Use the known working endpoint
                endpoint = f"{self.vllm_url}/completions"

                async with session.post(
                    endpoint,
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=120)
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        plan_content = result["choices"][0]["text"].strip()

                        # Try to parse as JSON
                        try:
                            plan_json = json.loads(plan_content)
                            
                            # Save plan to file
                            plan_filename = f"plan_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                            plan_path = Path("plans") / plan_filename
                            
                            with open(plan_path, 'w') as f:
                                json.dump(plan_json, f, indent=2)
                            
                            self._log_conversation(
                                "plan_generation_complete",
                                f"Plan saved to {plan_filename}",
                                {"plan_file": plan_filename, "changes_count": len(plan_json.get("changes", []))}
                            )
                            
                            return {
                                "plan": plan_json,
                                "plan_file": str(plan_path),
                                "status": "success"
                            }
                            
                        except json.JSONDecodeError as e:
                            self.logger.error(f"Failed to parse plan JSON: {e}")
                            return {
                                "plan": {"error": "Failed to parse JSON", "raw_content": plan_content},
                                "plan_file": None,
                                "status": "error"
                            }
                    else:
                        error_text = await response.text()
                        raise Exception(f"Plan generation failed: HTTP {response.status} from {endpoint}. Error: {error_text}")
                        
        except Exception as e:
            self.logger.error(f"Error generating plan: {e}")

            # Fallback: Create a simple plan without API call
            fallback_plan = {
                "summary": f"Analysis completed for: {user_request}",
                "changes": [
                    {
                        "file_path": "main.py",
                        "description": "Review and improve code structure",
                        "priority": 1,
                        "effort": "medium"
                    }
                ],
                "next_steps": [
                    "Review generated analysis",
                    "Check conversation log for detailed findings",
                    "Implement suggested improvements"
                ],
                "note": "Plan generated as fallback due to token limits"
            }

            # Save fallback plan to file
            plan_filename = f"plan_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            plan_path = Path("plans") / plan_filename
            plan_path.parent.mkdir(exist_ok=True)

            with open(plan_path, 'w') as f:
                json.dump(fallback_plan, f, indent=2)

            self._log_conversation(
                "plan_generation_fallback",
                f"Fallback plan saved to {plan_filename}",
                {"plan_file": plan_filename, "reason": "token_limit_exceeded"}
            )

            return {
                "plan": fallback_plan,
                "plan_file": str(plan_path),
                "status": "success_fallback"
            }
    
    def save_conversation_log(self) -> str:
        """Save the conversation log to file."""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        log_filename = f"conversation_{timestamp}.json"
        log_path = Path("conversation_logs") / log_filename
        
        # Convert conversation log to serializable format
        log_data = {
            "timestamp": datetime.now().isoformat(),
            "entries": [
                {
                    "timestamp": entry.timestamp.isoformat(),
                    "stage": entry.stage,
                    "content": entry.content,
                    "metadata": entry.metadata
                }
                for entry in self.conversation_log
            ]
        }
        
        with open(log_path, 'w') as f:
            json.dump(log_data, f, indent=2)
        
        self.logger.info(f"Conversation log saved to {log_filename}")
        return str(log_path)
    
    async def execute_full_workflow(self, folder_path: str, user_request: str) -> Dict[str, Any]:
        """Execute the complete hierarchical workflow."""
        self.logger.info("Starting hierarchical workflow execution")
        self._log_conversation("workflow_start", f"User request: {user_request}", {"folder_path": folder_path})
        
        try:
            # Step 1: Analyze local folder
            folder_info = await self.analyze_local_folder(folder_path)
            
            # Step 2: Process with BitNet agents
            bitnet_results = await self.process_with_bitnet_agents(folder_info, user_request)
            
            # Step 3: Reason with vLLM
            reasoning_result = await self.reason_with_vllm(bitnet_results, user_request)
            
            # Step 4: Generate plan.json
            plan_result = await self.generate_plan_json(reasoning_result, bitnet_results, user_request)
            
            # Step 5: Save conversation log
            conversation_log_path = self.save_conversation_log()
            
            self._log_conversation("workflow_complete", "Hierarchical workflow completed successfully")
            
            return {
                "status": "success",
                "folder_info": folder_info,
                "bitnet_results": [
                    {
                        "agent_role": r.agent_role,
                        "summary": r.summary,
                        "processing_time": r.processing_time
                    }
                    for r in bitnet_results
                ],
                "reasoning": reasoning_result,
                "plan": plan_result,
                "conversation_log": conversation_log_path,
                "total_processing_time": sum(r.processing_time for r in bitnet_results)
            }
            
        except Exception as e:
            self.logger.error(f"Workflow execution failed: {e}")
            self._log_conversation("workflow_error", f"Workflow failed: {str(e)}")
            
            return {
                "status": "error",
                "error": str(e),
                "conversation_log": self.save_conversation_log()
            }


async def main():
    """Main function for testing the hierarchical workflow."""
    print("🏗️  Hierarchical Workflow System")
    print("Local Folder → BitNet Agents → vLLM Reasoning → plan.json")
    print("=" * 60)
    
    # Initialize system
    system = HierarchicalWorkflowSystem()
    
    # Check vLLM health
    print("Checking vLLM server...")
    try:
        async def check_health():
            async with aiohttp.ClientSession() as session:
                # Try different model endpoints
                endpoints_to_try = [
                    f"{system.vllm_url}/models",
                    f"{system.vllm_url}/v1/models",
                    f"{system.vllm_url.replace('/v1', '')}/models",
                    f"{system.vllm_url.replace('/v1', '')}/v1/models"
                ]

                for endpoint in endpoints_to_try:
                    try:
                        async with session.get(endpoint, timeout=aiohttp.ClientTimeout(total=10)) as response:
                            if response.status == 200:
                                print(f"✅ vLLM server is available at {endpoint}")
                                return True
                    except Exception as e:
                        print(f"❌ Failed to connect to {endpoint}: {e}")
                        continue

                return False

        if await check_health():
            print("✅ vLLM server is available")
        else:
            print(f"❌ vLLM server not available at {system.vllm_url}")
            print("Please check your .env REASONING_MODEL_URL setting")
            return
    except Exception as e:
        print(f"❌ vLLM server health check failed: {e}")
        print(f"Please check your .env REASONING_MODEL_URL setting: {system.vllm_url}")
        return
    
    # Interactive mode
    print("\n🎯 Interactive Mode")
    print("Type 'quit' to exit")
    
    while True:
        try:
            folder_path = input("\nEnter folder path to analyze: ").strip()
            if folder_path.lower() in ['quit', 'exit']:
                break
            
            if not os.path.exists(folder_path):
                print("❌ Folder not found")
                continue
            
            user_request = input("Enter your request: ").strip()
            if not user_request:
                continue
            
            print(f"\n🚀 Executing hierarchical workflow...")
            result = await system.execute_full_workflow(folder_path, user_request)
            
            print("\n" + "=" * 60)
            print("📊 WORKFLOW RESULTS")
            print("=" * 60)
            
            if result["status"] == "success":
                print(f"✅ Status: {result['status']}")
                print(f"📁 Files analyzed: {len(result['folder_info']['files'])}")
                print(f"🤖 BitNet agents: {len(result['bitnet_results'])}")
                print(f"⏱️  Total processing time: {result['total_processing_time']:.2f}s")
                
                if result['plan']['status'] == 'success':
                    print(f"📋 Plan saved to: {result['plan']['plan_file']}")
                    plan = result['plan']['plan']
                    print(f"📝 Changes planned: {len(plan.get('changes', []))}")
                
                print(f"💬 Conversation log: {result['conversation_log']}")
            else:
                print(f"❌ Status: {result['status']}")
                print(f"Error: {result['error']}")
            
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"Error: {e}")
    
    print("\n👋 Goodbye!")


if __name__ == "__main__":
    asyncio.run(main())
