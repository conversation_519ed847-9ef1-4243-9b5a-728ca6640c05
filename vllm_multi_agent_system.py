#!/usr/bin/env python3
"""vLLM-based Multi-Agent System that simulates BitNet architecture."""

import asyncio
import aiohttp
import json
import time
from typing import Dict, Any, List
from dataclasses import dataclass
from datetime import datetime


@dataclass
class AgentResponse:
    """Response from a vLLM agent."""
    agent_role: str
    content: str
    processing_time: float
    chain_of_thought: List[str]


class VLLMMultiAgentSystem:
    """Multi-agent system using vLLM for both agents and reasoning."""
    
    def __init__(self, vllm_url: str = "http://localhost:8001/v1"):
        """Initialize the multi-agent system."""
        self.vllm_url = vllm_url
        self.agent_roles = {
            "analyzer": "You are an expert analyzer. Break down complex problems into components and provide detailed analysis.",
            "coder": "You are an expert programmer. Generate clean, efficient, and well-documented code solutions.",
            "reviewer": "You are an expert code reviewer. Analyze code for quality, security, and best practices.",
            "researcher": "You are an expert researcher. Gather information, synthesize findings, and provide insights."
        }
    
    async def call_vllm_agent(self, prompt: str, agent_role: str, enable_cot: bool = True) -> AgentResponse:
        """Call vLLM to simulate a specialized agent."""
        start_time = time.time()
        
        # Build agent-specific prompt
        system_prompt = self.agent_roles[agent_role]
        
        if enable_cot:
            full_prompt = f"{system_prompt}\n\nThink step by step about this request:\n{prompt}\n\nLet me think through this:"
        else:
            full_prompt = f"{system_prompt}\n\n{prompt}"
        
        try:
            async with aiohttp.ClientSession() as session:
                payload = {
                    "model": "llama-3.1-8b-instruct",
                    "prompt": full_prompt,
                    "max_tokens": 2048,
                    "temperature": 0.7,
                    "stop": ["Human:", "Assistant:"]
                }
                
                async with session.post(
                    f"{self.vllm_url}/completions",
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=60)
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        content = result["choices"][0]["text"].strip()
                        
                        # Extract chain of thought if present
                        chain_of_thought = []
                        if enable_cot and "Let me think through this:" in content:
                            parts = content.split("Let me think through this:", 1)
                            if len(parts) > 1:
                                chain_of_thought = [f"{agent_role} reasoning: {parts[1][:200]}..."]
                        
                        processing_time = time.time() - start_time
                        
                        return AgentResponse(
                            agent_role=agent_role,
                            content=content,
                            processing_time=processing_time,
                            chain_of_thought=chain_of_thought
                        )
                    else:
                        error_text = await response.text()
                        raise Exception(f"vLLM API error: {response.status} - {error_text}")
                        
        except Exception as e:
            processing_time = time.time() - start_time
            return AgentResponse(
                agent_role=agent_role,
                content=f"Error in {agent_role} agent: {str(e)}",
                processing_time=processing_time,
                chain_of_thought=[f"Error occurred in {agent_role}"]
            )
    
    async def reasoning_synthesis(self, task_description: str, agent_responses: List[AgentResponse]) -> Dict[str, Any]:
        """Use vLLM as reasoning model to synthesize agent responses."""
        start_time = time.time()
        
        # Build synthesis prompt
        synthesis_prompt = f"""You are an advanced reasoning agent that synthesizes outputs from multiple specialized agents to make final decisions.

TASK DESCRIPTION:
{task_description}

AGENT CONTRIBUTIONS:
"""
        
        for response in agent_responses:
            synthesis_prompt += f"\n--- {response.agent_role.upper()} AGENT ---\n"
            synthesis_prompt += f"Response: {response.content}\n"
            if response.chain_of_thought:
                synthesis_prompt += f"Reasoning: {' -> '.join(response.chain_of_thought)}\n"
        
        synthesis_prompt += """
REASONING INSTRUCTIONS:
1. Analyze each agent's contribution carefully
2. Identify strengths and weaknesses in each response
3. Look for consensus and conflicts between agents
4. Apply advanced reasoning to synthesize the best solution
5. Provide step-by-step reasoning for your decision
6. Assign a confidence score (0.0-1.0) to your final decision

RESPONSE FORMAT:
REASONING STEPS:
1. [First reasoning step]
2. [Second reasoning step]
3. [Continue as needed]

CONFIDENCE SCORE: [0.0-1.0]

FINAL DECISION:
[Your synthesized final decision/solution]
"""
        
        try:
            async with aiohttp.ClientSession() as session:
                payload = {
                    "model": "llama-3.1-8b-instruct",
                    "prompt": synthesis_prompt,
                    "max_tokens": 4096,
                    "temperature": 0.3,
                    "stop": ["Human:", "Assistant:"]
                }
                
                async with session.post(
                    f"{self.vllm_url}/completions",
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=120)
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        content = result["choices"][0]["text"].strip()
                        
                        # Parse the response
                        reasoning_steps = []
                        final_decision = ""
                        confidence = 0.5
                        
                        lines = content.split('\n')
                        current_section = None
                        
                        for line in lines:
                            line = line.strip()
                            
                            if line.startswith("REASONING STEPS:"):
                                current_section = "reasoning"
                                continue
                            elif line.startswith("CONFIDENCE SCORE:"):
                                current_section = "confidence"
                                try:
                                    confidence_str = line.split(":", 1)[1].strip()
                                    confidence = float(confidence_str)
                                except:
                                    confidence = 0.5
                                continue
                            elif line.startswith("FINAL DECISION:"):
                                current_section = "decision"
                                continue
                            
                            if current_section == "reasoning" and line:
                                if line[0].isdigit() or line.startswith("-"):
                                    reasoning_steps.append(line)
                            elif current_section == "decision" and line:
                                final_decision += line + "\n"
                        
                        final_decision = final_decision.strip()
                        if not final_decision:
                            final_decision = content
                        
                        processing_time = time.time() - start_time
                        
                        return {
                            "task_description": task_description,
                            "final_result": final_decision,
                            "reasoning_steps": reasoning_steps,
                            "confidence_score": confidence,
                            "agent_contributions": {r.agent_role: r.content for r in agent_responses},
                            "processing_time": processing_time,
                            "status": "completed"
                        }
                    else:
                        error_text = await response.text()
                        raise Exception(f"Reasoning synthesis failed: {response.status} - {error_text}")
                        
        except Exception as e:
            processing_time = time.time() - start_time
            return {
                "task_description": task_description,
                "final_result": f"Error in reasoning synthesis: {str(e)}",
                "reasoning_steps": [],
                "confidence_score": 0.0,
                "agent_contributions": {r.agent_role: r.content for r in agent_responses},
                "processing_time": processing_time,
                "status": "error"
            }
    
    async def process_task(self, task_description: str, selected_agents: List[str] = None) -> Dict[str, Any]:
        """Process a task using multiple vLLM agents + reasoning synthesis."""
        if selected_agents is None:
            selected_agents = list(self.agent_roles.keys())
        
        print(f"🚀 Processing task with {len(selected_agents)} agents...")
        print(f"📝 Task: {task_description}")
        
        # Step 1: Distribute task to agents (parallel processing)
        print("\n🤖 Calling specialized agents...")
        agent_tasks = []
        for agent_role in selected_agents:
            print(f"   - {agent_role.title()} agent")
            task = self.call_vllm_agent(task_description, agent_role, enable_cot=True)
            agent_tasks.append(task)
        
        # Execute all agent calls in parallel
        agent_responses = await asyncio.gather(*agent_tasks)
        
        # Step 2: Reasoning synthesis
        print("\n🧠 Synthesizing responses with reasoning model...")
        final_result = await self.reasoning_synthesis(task_description, agent_responses)
        
        return final_result
    
    async def check_vllm_health(self) -> bool:
        """Check if vLLM server is available."""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.vllm_url}/models",
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    return response.status == 200
        except:
            return False


async def main():
    """Main function to demonstrate the system."""
    print("🤖 vLLM Multi-Agent System")
    print("=" * 50)
    
    # Initialize system
    system = VLLMMultiAgentSystem()
    
    # Check vLLM health
    print("Checking vLLM server...")
    if await system.check_vllm_health():
        print("✅ vLLM server is available")
    else:
        print("❌ vLLM server not available at http://localhost:8001")
        print("Please start your vLLM server first:")
        print("vllm serve llama-3.1-8b-instruct --host 0.0.0.0 --port 8001")
        return
    
    # Interactive mode
    print("\n🎯 Interactive Mode")
    print("Type 'quit' to exit")
    
    while True:
        try:
            task = input("\nEnter your task: ").strip()
            
            if task.lower() in ['quit', 'exit']:
                break
            
            if not task:
                continue
            
            # Process the task
            result = await system.process_task(task)
            
            # Display results
            print("\n" + "=" * 60)
            print("📊 RESULTS")
            print("=" * 60)
            print(f"Status: {result['status']}")
            print(f"Confidence: {result['confidence_score']:.2f}")
            print(f"Processing Time: {result['processing_time']:.2f}s")
            
            print(f"\n🎯 Final Result:")
            print(result['final_result'])
            
            if result['reasoning_steps']:
                print(f"\n🧠 Reasoning Steps:")
                for i, step in enumerate(result['reasoning_steps'], 1):
                    print(f"   {i}. {step}")
            
            print(f"\n🤖 Agent Contributions:")
            for agent_role, contribution in result['agent_contributions'].items():
                print(f"\n--- {agent_role.upper()} ---")
                print(contribution[:200] + "..." if len(contribution) > 200 else contribution)
            
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"Error: {e}")
    
    print("\n👋 Goodbye!")


if __name__ == "__main__":
    asyncio.run(main())
