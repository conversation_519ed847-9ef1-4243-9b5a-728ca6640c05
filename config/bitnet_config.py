"""Configuration for BitNet Multi-Agent System."""

import os
from typing import Dict, Any, List
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


class BitNetConfig:
    """Configuration class for BitNet multi-agent system."""
    
    # ============================================================================
    # BITNET AGENTS CONFIGURATION
    # ============================================================================
    
    # Number of BitNet agents
    BITNET_AGENT_COUNT = int(os.getenv("BITNET_AGENT_COUNT", "4"))
    
    # BitNet model configuration
    BITNET_MODEL_NAME = os.getenv("BITNET_MODEL_NAME", "microsoft/BitNet-b1.58-2B-4T")
    BITNET_MODEL_PATH = os.getenv("BITNET_MODEL_PATH", "./models/BitNet-b1.58-2B-4T")
    BITNET_QUANTIZATION_TYPE = os.getenv("BITNET_QUANTIZATION_TYPE", "i2_s")
    
    # BitNet agent roles
    BITNET_AGENT_ROLES = os.getenv("BITNET_AGENT_ROLES", "analyzer,coder,reviewer,researcher").split(",")
    
    # BitNet inference settings
    BITNET_MAX_TOKENS = int(os.getenv("BITNET_MAX_TOKENS", "4096"))
    BITNET_TEMPERATURE = float(os.getenv("BITNET_TEMPERATURE", "0.7"))
    BITNET_TOP_P = float(os.getenv("BITNET_TOP_P", "0.9"))
    BITNET_THREADS = int(os.getenv("BITNET_THREADS", "4"))
    
    # ============================================================================
    # REASONING MODEL CONFIGURATION
    # ============================================================================
    
    # Reasoning model (vLLM) settings
    REASONING_MODEL_URL = os.getenv("REASONING_MODEL_URL", "http://localhost:8001/v1")
    REASONING_MODEL_NAME = os.getenv("REASONING_MODEL_NAME", "llama-3.1-8b-instruct")
    REASONING_MODEL_API_KEY = os.getenv("REASONING_MODEL_API_KEY", "your-api-key-here")
    
    # Reasoning model parameters
    REASONING_MAX_TOKENS = int(os.getenv("REASONING_MAX_TOKENS", "8196"))
    REASONING_TEMPERATURE = float(os.getenv("REASONING_TEMPERATURE", "0.3"))
    REASONING_TOP_P = float(os.getenv("REASONING_TOP_P", "0.95"))
    
    # ============================================================================
    # DOCKER CONFIGURATION
    # ============================================================================
    
    # Docker settings
    DOCKER_ENABLED = os.getenv("DOCKER_ENABLED", "true").lower() == "true"
    BITNET_DOCKER_IMAGE = os.getenv("BITNET_DOCKER_IMAGE", "bitnet-agent:latest")
    DOCKER_NETWORK = os.getenv("DOCKER_NETWORK", "bitnet-network")
    
    # Container resource limits
    BITNET_CONTAINER_CPU_LIMIT = os.getenv("BITNET_CONTAINER_CPU_LIMIT", "2")
    BITNET_CONTAINER_MEMORY_LIMIT = os.getenv("BITNET_CONTAINER_MEMORY_LIMIT", "4g")
    
    # ============================================================================
    # ORCHESTRATION SETTINGS
    # ============================================================================
    
    # Communication settings
    AGENT_COMMUNICATION_TIMEOUT = int(os.getenv("AGENT_COMMUNICATION_TIMEOUT", "30"))
    MAX_AGENT_RETRIES = int(os.getenv("MAX_AGENT_RETRIES", "3"))
    ENABLE_CHAIN_OF_THOUGHT = os.getenv("ENABLE_CHAIN_OF_THOUGHT", "true").lower() == "true"
    ENABLE_MIXTURE_OF_EXPERTS = os.getenv("ENABLE_MIXTURE_OF_EXPERTS", "true").lower() == "true"
    
    # Task distribution
    MAX_CONTEXT_LENGTH = int(os.getenv("MAX_CONTEXT_LENGTH", "32768"))
    CONTEXT_OVERLAP = int(os.getenv("CONTEXT_OVERLAP", "512"))
    PARALLEL_PROCESSING = os.getenv("PARALLEL_PROCESSING", "true").lower() == "true"
    
    # ============================================================================
    # MEMORY AND LOGGING
    # ============================================================================
    
    # Memory configuration
    MEMORY_FOLDER = os.getenv("MEMORY_FOLDER", "./memory_logs")
    ENABLE_PERSISTENT_MEMORY = os.getenv("ENABLE_PERSISTENT_MEMORY", "true").lower() == "true"
    MEMORY_CONSOLIDATION_INTERVAL = int(os.getenv("MEMORY_CONSOLIDATION_INTERVAL", "100"))
    
    # Logging
    LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
    ENABLE_DETAILED_LOGGING = os.getenv("ENABLE_DETAILED_LOGGING", "true").lower() == "true"
    LOG_FOLDER = os.getenv("LOG_FOLDER", "./logs")
    
    # ============================================================================
    # PERFORMANCE TUNING
    # ============================================================================
    
    # Performance settings
    ENABLE_CACHING = os.getenv("ENABLE_CACHING", "true").lower() == "true"
    CACHE_SIZE = int(os.getenv("CACHE_SIZE", "1000"))
    BATCH_SIZE = int(os.getenv("BATCH_SIZE", "4"))
    ASYNC_PROCESSING = os.getenv("ASYNC_PROCESSING", "true").lower() == "true"
    
    # Resource monitoring
    ENABLE_RESOURCE_MONITORING = os.getenv("ENABLE_RESOURCE_MONITORING", "true").lower() == "true"
    RESOURCE_CHECK_INTERVAL = int(os.getenv("RESOURCE_CHECK_INTERVAL", "10"))
    
    # ============================================================================
    # NETWORKING
    # ============================================================================
    
    # Network settings
    BITNET_BASE_PORT = int(os.getenv("BITNET_BASE_PORT", "9000"))
    REASONING_AGENT_PORT = int(os.getenv("REASONING_AGENT_PORT", "9100"))
    ORCHESTRATOR_PORT = int(os.getenv("ORCHESTRATOR_PORT", "9200"))
    
    # Redis settings
    REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379")
    REDIS_DB = int(os.getenv("REDIS_DB", "0"))
    
    # ============================================================================
    # SECURITY
    # ============================================================================
    
    # Security settings
    ENABLE_API_AUTHENTICATION = os.getenv("ENABLE_API_AUTHENTICATION", "false").lower() == "true"
    API_SECRET_KEY = os.getenv("API_SECRET_KEY", "your-secret-key-here")
    
    @classmethod
    def get_agent_config(cls, agent_id: str, agent_role: str) -> Dict[str, Any]:
        """Get configuration for a specific agent."""
        return {
            "agent_id": agent_id,
            "agent_role": agent_role,
            "model_path": cls.BITNET_MODEL_PATH,
            "max_tokens": cls.BITNET_MAX_TOKENS,
            "temperature": cls.BITNET_TEMPERATURE,
            "top_p": cls.BITNET_TOP_P,
            "threads": cls.BITNET_THREADS,
            "enable_cot": cls.ENABLE_CHAIN_OF_THOUGHT,
            "port": cls.BITNET_BASE_PORT + int(agent_id)
        }
    
    @classmethod
    def get_reasoning_config(cls) -> Dict[str, Any]:
        """Get configuration for the reasoning model."""
        return {
            "base_url": cls.REASONING_MODEL_URL,
            "model": cls.REASONING_MODEL_NAME,
            "api_key": cls.REASONING_MODEL_API_KEY,
            "max_tokens": cls.REASONING_MAX_TOKENS,
            "temperature": cls.REASONING_TEMPERATURE,
            "top_p": cls.REASONING_TOP_P
        }
    
    @classmethod
    def get_orchestrator_config(cls) -> Dict[str, Any]:
        """Get configuration for the orchestrator."""
        return {
            "agent_count": cls.BITNET_AGENT_COUNT,
            "agent_roles": cls.BITNET_AGENT_ROLES,
            "communication_timeout": cls.AGENT_COMMUNICATION_TIMEOUT,
            "max_retries": cls.MAX_AGENT_RETRIES,
            "enable_moe": cls.ENABLE_MIXTURE_OF_EXPERTS,
            "enable_cot": cls.ENABLE_CHAIN_OF_THOUGHT,
            "max_context_length": cls.MAX_CONTEXT_LENGTH,
            "context_overlap": cls.CONTEXT_OVERLAP,
            "parallel_processing": cls.PARALLEL_PROCESSING,
            "redis_url": cls.REDIS_URL,
            "port": cls.ORCHESTRATOR_PORT
        }
    
    @classmethod
    def ensure_directories(cls):
        """Ensure all required directories exist."""
        import os
        
        directories = [
            cls.MEMORY_FOLDER,
            cls.LOG_FOLDER,
            cls.BITNET_MODEL_PATH,
            "./models",
            "./docker/bitnet",
            "./orchestrator",
            "./agents"
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
    
    @classmethod
    def validate_config(cls) -> List[str]:
        """Validate configuration and return any errors."""
        errors = []
        
        # Check required paths
        if not os.path.exists(cls.BITNET_MODEL_PATH):
            errors.append(f"BitNet model path does not exist: {cls.BITNET_MODEL_PATH}")
        
        # Check agent count and roles
        if cls.BITNET_AGENT_COUNT != len(cls.BITNET_AGENT_ROLES):
            errors.append(f"Agent count ({cls.BITNET_AGENT_COUNT}) does not match number of roles ({len(cls.BITNET_AGENT_ROLES)})")
        
        # Check reasoning model URL
        if not cls.REASONING_MODEL_URL.startswith(("http://", "https://")):
            errors.append(f"Invalid reasoning model URL: {cls.REASONING_MODEL_URL}")
        
        return errors
