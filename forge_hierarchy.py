#!/usr/bin/env python3
"""
🔥 Forge Hierarchy - Multi-Agent System with BitNet and Hierarchical Workflow

Local Folder → BitNet Agents → vLLM Reasoning → plan.json + Conversation Log

Usage:
    python forge_hierarchy.py                    # Launch GUI
    python forge_hierarchy.py --cli              # Command line interface
    python forge_hierarchy.py --folder /path     # Direct folder processing
"""

import argparse
import asyncio
import os
import sys
from pathlib import Path

# Add current directory to path
sys.path.append(str(Path(__file__).parent))

def launch_gui():
    """Launch the Forge Hierarchy GUI."""
    print("🔥 Launching Forge Hierarchy GUI...")

    # Find the GUI path
    current_dir = Path(__file__).parent
    gui_path = current_dir / "multi-agent-workflow"

    # Try alternative paths
    if not gui_path.exists():
        gui_path = current_dir.parent / "multi-agent-workflow"

    if not gui_path.exists():
        print("❌ GUI directory not found.")
        print("Looking for multi-agent-workflow directory...")
        print(f"Tried: {current_dir / 'multi-agent-workflow'}")
        print(f"Tried: {current_dir.parent / 'multi-agent-workflow'}")
        return

    print(f"✅ Found GUI at: {gui_path}")
    os.chdir(gui_path)
    
    # Launch Streamlit
    import subprocess
    try:
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", 
            "gui/streamlit_app.py", 
            "--server.port", "8502",
            "--server.address", "localhost"
        ], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to launch GUI: {e}")
    except KeyboardInterrupt:
        print("\n👋 Forge Hierarchy GUI stopped")

async def run_cli():
    """Run Forge Hierarchy in command line mode."""
    print("🔥 Forge Hierarchy - Command Line Interface")
    print("Local Folder → BitNet Agents → vLLM Reasoning → plan.json")
    print("=" * 60)
    
    # Import the hierarchical workflow system
    try:
        from hierarchical_workflow_system import HierarchicalWorkflowSystem
    except ImportError:
        print("❌ Could not import HierarchicalWorkflowSystem")
        print("Make sure you're running from the correct directory")
        return
    
    # Initialize system
    system = HierarchicalWorkflowSystem()
    
    # Check vLLM health
    print("Checking vLLM server...")
    try:
        import aiohttp
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{system.vllm_url}/models", timeout=aiohttp.ClientTimeout(total=10)) as response:
                if response.status == 200:
                    print("✅ vLLM server is available")
                else:
                    print("❌ vLLM server not responding properly")
                    return
    except:
        print("❌ vLLM server not available at http://localhost:8001")
        print("Please start your vLLM server first:")
        print("vllm serve llama-3.1-8b-instruct --host 0.0.0.0 --port 8001")
        return
    
    # Interactive mode
    print("\n🎯 Interactive Mode")
    print("Type 'quit' to exit")
    
    while True:
        try:
            folder_path = input("\nEnter folder path to analyze: ").strip()
            if folder_path.lower() in ['quit', 'exit']:
                break
            
            if not os.path.exists(folder_path):
                print("❌ Folder not found")
                continue
            
            user_request = input("Enter your request: ").strip()
            if not user_request:
                continue
            
            print(f"\n🚀 Executing Forge Hierarchy...")
            result = await system.execute_full_workflow(folder_path, user_request)
            
            print("\n" + "=" * 60)
            print("📊 FORGE HIERARCHY RESULTS")
            print("=" * 60)
            
            if result["status"] == "success":
                print(f"✅ Status: {result['status']}")
                print(f"📁 Files analyzed: {len(result['folder_info']['files'])}")
                print(f"🤖 BitNet agents: {len(result['bitnet_results'])}")
                print(f"⏱️  Total processing time: {result['total_processing_time']:.2f}s")
                
                if result['plan']['status'] == 'success':
                    print(f"📋 Plan saved to: {result['plan']['plan_file']}")
                    plan = result['plan']['plan']
                    print(f"📝 Changes planned: {len(plan.get('changes', []))}")
                
                print(f"💬 Conversation log: {result['conversation_log']}")
            else:
                print(f"❌ Status: {result['status']}")
                print(f"Error: {result['error']}")
            
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"Error: {e}")
    
    print("\n👋 Goodbye!")

async def process_folder(folder_path: str, request: str = None):
    """Process a specific folder with Forge Hierarchy."""
    print(f"🔥 Processing folder: {folder_path}")
    
    if not os.path.exists(folder_path):
        print(f"❌ Folder not found: {folder_path}")
        return
    
    if not request:
        request = input("Enter your request: ").strip()
        if not request:
            print("❌ No request provided")
            return
    
    # Import and run
    try:
        from hierarchical_workflow_system import HierarchicalWorkflowSystem
        system = HierarchicalWorkflowSystem()
        
        print("🚀 Executing Forge Hierarchy...")
        result = await system.execute_full_workflow(folder_path, request)
        
        if result["status"] == "success":
            print("✅ Forge Hierarchy completed successfully!")
            if result['plan']['status'] == 'success':
                print(f"📋 Plan saved to: {result['plan']['plan_file']}")
            print(f"💬 Conversation log: {result['conversation_log']}")
        else:
            print(f"❌ Forge Hierarchy failed: {result['error']}")
            
    except ImportError:
        print("❌ Could not import HierarchicalWorkflowSystem")
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    """Main entry point for Forge Hierarchy."""
    parser = argparse.ArgumentParser(
        description="🔥 Forge Hierarchy - Multi-Agent System with BitNet and Hierarchical Workflow",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python forge_hierarchy.py                           # Launch GUI
  python forge_hierarchy.py --cli                     # Command line interface
  python forge_hierarchy.py --folder /path/to/code    # Process specific folder
  
Workflow:
  Local Folder → BitNet Agents → vLLM Reasoning → plan.json + Conversation Log
        """
    )
    
    parser.add_argument(
        "--cli", 
        action="store_true", 
        help="Run in command line interface mode"
    )
    
    parser.add_argument(
        "--folder", 
        type=str, 
        help="Process a specific folder path"
    )
    
    parser.add_argument(
        "--request", 
        type=str, 
        help="Request to process (used with --folder)"
    )
    
    args = parser.parse_args()
    
    # Print banner
    print("🔥" * 20)
    print("🔥 FORGE HIERARCHY")
    print("🔥 Multi-Agent System with BitNet and Hierarchical Workflow")
    print("🔥" * 20)
    print()
    
    if args.folder:
        # Process specific folder
        asyncio.run(process_folder(args.folder, args.request))
    elif args.cli:
        # Command line interface
        asyncio.run(run_cli())
    else:
        # Launch GUI (default)
        launch_gui()

if __name__ == "__main__":
    main()
