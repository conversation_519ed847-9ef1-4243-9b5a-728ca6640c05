#!/usr/bin/env python3
"""Debug script to test vLLM API calls."""

import asyncio
import aiohttp
import json
import os
from dotenv import load_dotenv

load_dotenv()

async def test_api_call():
    """Test the exact API call that's failing."""
    vllm_url = os.getenv('REASONING_MODEL_URL', 'http://*************:8001/v1')
    model_name = os.getenv('REASONING_MODEL_NAME', '/models/DeepSeek-R1-Distill-Llama-8B-Q6_K.gguf')
    
    print(f"🔧 Testing API call to: {vllm_url}")
    print(f"🤖 Model: {model_name}")
    
    # Test the exact payload that's failing
    payload = {
        "model": model_name,
        "prompt": "Hello, how are you?",
        "max_tokens": 50,
        "temperature": 0.7,
        "stop": ["Human:", "Assistant:"]
    }
    
    print(f"📋 Payload: {json.dumps(payload, indent=2)}")
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.post(
                f"{vllm_url}/completions",
                json=payload,
                timeout=aiohttp.ClientTimeout(total=30)
            ) as response:
                print(f"📊 Response status: {response.status}")
                print(f"📋 Response headers: {dict(response.headers)}")
                
                if response.status == 200:
                    result = await response.json()
                    print(f"✅ Success!")
                    print(f"📝 Response: {json.dumps(result, indent=2)}")
                else:
                    error_text = await response.text()
                    print(f"❌ Error response: {error_text}")
                    
                    # Try without stop parameter
                    print("\n🔄 Trying without stop parameter...")
                    payload_no_stop = {
                        "model": model_name,
                        "prompt": "Hello, how are you?",
                        "max_tokens": 50,
                        "temperature": 0.7
                    }
                    
                    async with session.post(
                        f"{vllm_url}/completions",
                        json=payload_no_stop,
                        timeout=aiohttp.ClientTimeout(total=30)
                    ) as response2:
                        print(f"📊 Response status (no stop): {response2.status}")
                        if response2.status == 200:
                            result2 = await response2.json()
                            print(f"✅ Success without stop parameter!")
                            print(f"📝 Response: {json.dumps(result2, indent=2)}")
                        else:
                            error_text2 = await response2.text()
                            print(f"❌ Still error: {error_text2}")
                            
        except Exception as e:
            print(f"❌ Exception: {e}")

if __name__ == "__main__":
    asyncio.run(test_api_call())
