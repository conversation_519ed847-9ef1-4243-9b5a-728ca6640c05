# BitNet Multi-Agent System

A sophisticated multi-agent system that leverages Microsoft's BitNet 1.58-bit models for large context processing and understanding, combined with a reasoning model for final decision making.

## Architecture

- **BitNet Agents**: Multiple 1-bit models running on CPU for efficient large context processing
- **Reasoning Model**: vLLM-powered model for final reasoning and decision making
- **Docker Integration**: Scalable container-based deployment
- **Chain of Thought**: Forced CoT processing across agents
- **Mixture of Experts**: Multiple specialized BitNet agents

## Quick Start

1. Clone and setup:
```bash
git clone <repo>
cd 1-bit
python setup_bitnet_system.py
```

2. Configure environment:
```bash
cp .env.example .env
# Edit .env with your settings
```

3. Run the system:
```bash
python run_multi_agent_system.py
```

## Configuration

All settings are configurable via `.env` file:
- Number of BitNet agents
- Model names and paths
- Resource allocation
- Communication settings

## Features

- Scalable BitNet agent deployment
- Intelligent task distribution
- Chain of Thought processing
- Memory management
- Docker containerization
- Flexible configuration
