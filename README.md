# 🔥 Forge Hierarchy

**Multi-Agent System with BitNet and Hierarchical Workflow**

Transform your codebase analysis and planning with a powerful hierarchical workflow that combines efficient BitNet 1-bit models with strategic vLLM reasoning.

## 🏗️ Architecture

```
Local Folder → BitNet Agents → vLLM Reasoning → plan.json + Conversation Log
     ↓              ↓              ↓              ↓
File Analysis  Code Analysis   Strategic Plan  Action Items
(15k-20k tokens) (4 Specialists) (8k context)  (JSON output)
```

### 🤖 BitNet Agents (1-bit Models)
- **File Analyzer**: Analyzes code structure, dependencies, imports
- **Code Generator**: Creates code solutions based on patterns  
- **Context Summarizer**: Condenses large codebases for reasoning
- **Change Detector**: Identifies required modifications

### 🧠 vLLM Reasoning Layer
- **Strategic Planning**: High-level decision making
- **Limited Context**: Optimized for 8k token reasoning
- **Plan Generation**: Structured JSON output

## 🚀 Quick Start

### Prerequisites
```bash
# Install dependencies
pip install -r requirements.txt

# Start vLLM server
vllm serve llama-3.1-8b-instruct --host 0.0.0.0 --port 8001 --max-model-len 8196
```

### Launch Forge Hierarchy

**GUI Mode (Recommended):**
```bash
python forge_hierarchy.py
```

**Command Line Mode:**
```bash
python forge_hierarchy.py --cli
```

**Direct Folder Processing:**
```bash
python forge_hierarchy.py --folder /path/to/your/project
```

## 🎯 Usage Examples

### 1. Codebase Refactoring
```bash
# Analyze and plan refactoring
Request: "Refactor this codebase to use dependency injection and improve testability"
```

### 2. Feature Implementation
```bash
# Plan new feature implementation
Request: "Add comprehensive error handling and logging throughout the application"
```

### 3. Architecture Analysis
```bash
# Analyze and improve architecture
Request: "Analyze the current architecture and suggest improvements for scalability"
```

## 📋 Output Files

### plan.json
Structured implementation plan with:
- **File Changes**: create/modify/delete operations
- **Priority Levels**: 1-10 priority scoring
- **Effort Estimates**: Time and complexity estimates
- **Code Snippets**: Relevant code examples
- **Dependencies**: Change dependencies and order
- **Risk Assessment**: Potential risks and mitigation

### Conversation Log
Complete workflow trace with:
- **Timestamps**: For each processing stage
- **Agent Processing**: BitNet agent outputs and summaries
- **Reasoning Steps**: vLLM strategic decisions
- **Metadata**: Performance metrics and processing details

## ⚙️ Configuration

### Environment Variables (.env)
```bash
# BitNet Configuration
BITNET_AGENT_ROLES=file_analyzer,code_generator,context_summarizer,change_detector
BITNET_MAX_TOKENS=20000
BITNET_TEMPERATURE=0.7

# vLLM Configuration  
REASONING_MODEL_NAME=llama-3.1-8b-instruct
REASONING_MODEL_URL=http://localhost:8001/v1
REASONING_MAX_TOKENS=8196
REASONING_TEMPERATURE=0.3

# Output Configuration
CONVERSATION_LOG_FOLDER=./conversation_logs
PLAN_OUTPUT_FOLDER=./plans
ENABLE_CONVERSATION_LOGGING=true
ENABLE_PLAN_JSON_OUTPUT=true
```

## 🔧 System Requirements

### Hardware
- **CPU**: Multi-core processor (BitNet agents run in parallel)
- **RAM**: 8GB+ recommended for large codebases
- **Storage**: SSD recommended for fast file I/O

### Software
- **Python**: 3.8+
- **vLLM**: Latest version
- **BitNet Models**: 1-bit quantized models (simulated with vLLM for now)

## 📊 Performance

### Typical Processing Times
- **Small Project** (< 50 files): 30-60 seconds
- **Medium Project** (50-200 files): 1-3 minutes  
- **Large Project** (200+ files): 3-10 minutes

### Token Handling
- **BitNet Agents**: Handle 15k-20k tokens efficiently
- **vLLM Reasoning**: Optimized for 8k context window
- **Parallel Processing**: 4 agents work simultaneously

## 🎨 GUI Features

### Enhanced Folder Browser
- **Common Locations**: Quick access to typical project folders
- **Folder Preview**: File statistics and structure overview
- **Real-time Validation**: Instant folder existence checking

### Progress Tracking
- **4-Stage Progress**: Folder → BitNet → vLLM → Plan
- **Real-time Updates**: Live status and progress indicators
- **Error Handling**: Clear error messages and recovery suggestions

### Results Display
- **Plan Visualization**: Interactive plan.json viewer
- **Agent Summaries**: Individual BitNet agent outputs
- **Reasoning Steps**: vLLM strategic decision process
- **Download Options**: Export plans and conversation logs

## 🔍 Troubleshooting

### vLLM Server Issues
```bash
# Check if vLLM is running
curl http://localhost:8001/v1/models

# Restart vLLM server
vllm serve llama-3.1-8b-instruct --host 0.0.0.0 --port 8001
```

### Large Codebase Handling
- **File Limits**: System handles 1000+ files efficiently
- **Memory Management**: Automatic cleanup and optimization
- **Token Truncation**: Smart truncation for very large files

### Common Errors
- **Folder Not Found**: Ensure folder path exists and is accessible
- **vLLM Connection**: Verify vLLM server is running on port 8001
- **Permission Denied**: Check folder read permissions

## 🤝 Contributing

1. **Fork** the repository
2. **Create** a feature branch
3. **Implement** your changes
4. **Test** with various codebases
5. **Submit** a pull request

## 📄 License

MIT License - see LICENSE file for details

## 🙏 Acknowledgments

- **BitNet**: Efficient 1-bit model architecture
- **vLLM**: High-performance LLM inference
- **Streamlit**: Beautiful web interface framework

---

**🔥 Forge Hierarchy - Where BitNet meets Strategic Reasoning**
