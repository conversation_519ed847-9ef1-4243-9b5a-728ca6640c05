2025-05-31 13:56:05,947 - hierarchical_workflow - INFO - Starting hierarchical workflow execution
2025-05-31 13:56:05,947 - hierarchical_workflow - INFO - [workflow_start] User request: Analyze this Python project and suggest improvements...
2025-05-31 13:56:05,947 - hierarchical_workflow - INFO - [folder_analysis] Analyzing folder: test-project...
2025-05-31 13:56:05,948 - hierarchical_workflow - INFO - [folder_analysis_complete] Analyzed 3 files, ~19 tokens...
2025-05-31 13:56:05,948 - hierarchical_workflow - INFO - [bitnet_processing_start] Processing with 4 BitNet agents...
2025-05-31 13:56:05,948 - hierarchical_workflow - INFO - Processing with file_analyzer agent...
2025-05-31 13:56:05,968 - hierarchical_workflow - ERROR - Error in file_analyzer agent: BitNet simulation failed: 404
2025-05-31 13:56:05,968 - hierarchical_workflow - INFO - [bitnet_agent_complete] file_analyzer completed analysis...
2025-05-31 13:56:05,968 - hierarchical_workflow - INFO - Processing with code_generator agent...
2025-05-31 13:56:05,971 - hierarchical_workflow - ERROR - Error in code_generator agent: BitNet simulation failed: 404
2025-05-31 13:56:05,971 - hierarchical_workflow - INFO - [bitnet_agent_complete] code_generator completed analysis...
2025-05-31 13:56:05,971 - hierarchical_workflow - INFO - Processing with context_summarizer agent...
2025-05-31 13:56:05,974 - hierarchical_workflow - ERROR - Error in context_summarizer agent: BitNet simulation failed: 404
2025-05-31 13:56:05,974 - hierarchical_workflow - INFO - [bitnet_agent_complete] context_summarizer completed analysis...
2025-05-31 13:56:05,974 - hierarchical_workflow - INFO - Processing with change_detector agent...
2025-05-31 13:56:05,976 - hierarchical_workflow - ERROR - Error in change_detector agent: BitNet simulation failed: 404
2025-05-31 13:56:05,976 - hierarchical_workflow - INFO - [bitnet_agent_complete] change_detector completed analysis...
2025-05-31 13:56:05,976 - hierarchical_workflow - INFO - [vllm_reasoning_start] Starting vLLM reasoning phase...
2025-05-31 13:56:05,977 - hierarchical_workflow - ERROR - Error in vLLM reasoning: vLLM reasoning failed: 404
2025-05-31 13:56:05,977 - hierarchical_workflow - INFO - [plan_generation_start] Generating plan.json...
2025-05-31 13:56:05,979 - hierarchical_workflow - ERROR - Error generating plan: Plan generation failed: 404
2025-05-31 13:56:05,979 - hierarchical_workflow - INFO - Conversation log saved to conversation_20250531_135605.json
2025-05-31 13:56:05,980 - hierarchical_workflow - INFO - [workflow_complete] Hierarchical workflow completed successfully...
2025-05-31 13:59:01,079 - hierarchical_workflow - INFO - Starting hierarchical workflow execution
2025-05-31 13:59:01,079 - hierarchical_workflow - INFO - [workflow_start] User request: Analyze this Python project...
2025-05-31 13:59:01,080 - hierarchical_workflow - INFO - [folder_analysis] Analyzing folder: test-project...
2025-05-31 13:59:01,080 - hierarchical_workflow - INFO - [folder_analysis_complete] Analyzed 3 files, ~19 tokens...
2025-05-31 13:59:01,080 - hierarchical_workflow - INFO - [bitnet_processing_start] Processing with 4 BitNet agents...
2025-05-31 13:59:01,080 - hierarchical_workflow - INFO - Processing with file_analyzer agent...
2025-05-31 13:59:01,086 - hierarchical_workflow - ERROR - Error in file_analyzer agent: All API endpoints failed. Last error: HTTP 404 from http://172.18.18.192:8001/v1/completions
2025-05-31 13:59:01,086 - hierarchical_workflow - INFO - [bitnet_agent_complete] file_analyzer completed analysis...
2025-05-31 13:59:01,086 - hierarchical_workflow - INFO - Processing with code_generator agent...
2025-05-31 13:59:01,091 - hierarchical_workflow - ERROR - Error in code_generator agent: All API endpoints failed. Last error: HTTP 404 from http://172.18.18.192:8001/v1/completions
2025-05-31 13:59:01,091 - hierarchical_workflow - INFO - [bitnet_agent_complete] code_generator completed analysis...
2025-05-31 13:59:01,091 - hierarchical_workflow - INFO - Processing with context_summarizer agent...
2025-05-31 13:59:01,095 - hierarchical_workflow - ERROR - Error in context_summarizer agent: All API endpoints failed. Last error: HTTP 404 from http://172.18.18.192:8001/v1/completions
2025-05-31 13:59:01,095 - hierarchical_workflow - INFO - [bitnet_agent_complete] context_summarizer completed analysis...
2025-05-31 13:59:01,095 - hierarchical_workflow - INFO - Processing with change_detector agent...
2025-05-31 13:59:01,100 - hierarchical_workflow - ERROR - Error in change_detector agent: All API endpoints failed. Last error: HTTP 404 from http://172.18.18.192:8001/v1/completions
2025-05-31 13:59:01,100 - hierarchical_workflow - INFO - [bitnet_agent_complete] change_detector completed analysis...
2025-05-31 13:59:01,100 - hierarchical_workflow - INFO - [vllm_reasoning_start] Starting vLLM reasoning phase...
2025-05-31 13:59:01,105 - hierarchical_workflow - ERROR - Error in vLLM reasoning: vLLM reasoning failed - all endpoints failed. Last error: HTTP 404 from http://172.18.18.192:8001/v1/completions
2025-05-31 13:59:01,105 - hierarchical_workflow - INFO - [plan_generation_start] Generating plan.json...
2025-05-31 13:59:01,107 - hierarchical_workflow - ERROR - Error generating plan: Plan generation failed: 404
2025-05-31 13:59:01,107 - hierarchical_workflow - INFO - Conversation log saved to conversation_20250531_135901.json
2025-05-31 13:59:01,107 - hierarchical_workflow - INFO - [workflow_complete] Hierarchical workflow completed successfully...
2025-05-31 14:02:12,641 - hierarchical_workflow - INFO - Starting hierarchical workflow execution
2025-05-31 14:02:12,642 - hierarchical_workflow - INFO - [workflow_start] User request: Analyze this Python project and suggest improvements...
2025-05-31 14:02:12,642 - hierarchical_workflow - INFO - [folder_analysis] Analyzing folder: test-project...
2025-05-31 14:02:12,642 - hierarchical_workflow - INFO - [folder_analysis_complete] Analyzed 3 files, ~19 tokens...
2025-05-31 14:02:12,642 - hierarchical_workflow - INFO - [bitnet_processing_start] Processing with 4 BitNet agents...
2025-05-31 14:02:12,642 - hierarchical_workflow - INFO - Processing with file_analyzer agent...
2025-05-31 14:02:12,656 - hierarchical_workflow - ERROR - Error in file_analyzer agent: All API endpoints failed. Last error: HTTP 400 from http://172.18.18.192:8001/v1/completions
2025-05-31 14:02:12,656 - hierarchical_workflow - INFO - [bitnet_agent_complete] file_analyzer completed analysis...
2025-05-31 14:02:12,656 - hierarchical_workflow - INFO - Processing with code_generator agent...
2025-05-31 14:02:12,661 - hierarchical_workflow - ERROR - Error in code_generator agent: All API endpoints failed. Last error: HTTP 400 from http://172.18.18.192:8001/v1/completions
2025-05-31 14:02:12,661 - hierarchical_workflow - INFO - [bitnet_agent_complete] code_generator completed analysis...
2025-05-31 14:02:12,661 - hierarchical_workflow - INFO - Processing with context_summarizer agent...
2025-05-31 14:02:12,666 - hierarchical_workflow - ERROR - Error in context_summarizer agent: All API endpoints failed. Last error: HTTP 400 from http://172.18.18.192:8001/v1/completions
2025-05-31 14:02:12,666 - hierarchical_workflow - INFO - [bitnet_agent_complete] context_summarizer completed analysis...
2025-05-31 14:02:12,666 - hierarchical_workflow - INFO - Processing with change_detector agent...
2025-05-31 14:02:12,674 - hierarchical_workflow - ERROR - Error in change_detector agent: All API endpoints failed. Last error: HTTP 400 from http://172.18.18.192:8001/v1/completions
2025-05-31 14:02:12,674 - hierarchical_workflow - INFO - [bitnet_agent_complete] change_detector completed analysis...
2025-05-31 14:02:12,674 - hierarchical_workflow - INFO - [vllm_reasoning_start] Starting vLLM reasoning phase...
2025-05-31 14:02:12,682 - hierarchical_workflow - ERROR - Error in vLLM reasoning: vLLM reasoning failed - all endpoints failed. Last error: HTTP 400 from http://172.18.18.192:8001/v1/completions
2025-05-31 14:02:12,682 - hierarchical_workflow - INFO - [plan_generation_start] Generating plan.json...
2025-05-31 14:02:12,684 - hierarchical_workflow - ERROR - Error generating plan: Plan generation failed: 404
2025-05-31 14:02:12,684 - hierarchical_workflow - INFO - Conversation log saved to conversation_20250531_140212.json
2025-05-31 14:02:12,684 - hierarchical_workflow - INFO - [workflow_complete] Hierarchical workflow completed successfully...
