2025-05-31 13:56:05,947 - hierarchical_workflow - INFO - Starting hierarchical workflow execution
2025-05-31 13:56:05,947 - hierarchical_workflow - INFO - [workflow_start] User request: Analyze this Python project and suggest improvements...
2025-05-31 13:56:05,947 - hierarchical_workflow - INFO - [folder_analysis] Analyzing folder: test-project...
2025-05-31 13:56:05,948 - hierarchical_workflow - INFO - [folder_analysis_complete] Analyzed 3 files, ~19 tokens...
2025-05-31 13:56:05,948 - hierarchical_workflow - INFO - [bitnet_processing_start] Processing with 4 BitNet agents...
2025-05-31 13:56:05,948 - hierarchical_workflow - INFO - Processing with file_analyzer agent...
2025-05-31 13:56:05,968 - hierarchical_workflow - ERROR - Error in file_analyzer agent: BitNet simulation failed: 404
2025-05-31 13:56:05,968 - hierarchical_workflow - INFO - [bitnet_agent_complete] file_analyzer completed analysis...
2025-05-31 13:56:05,968 - hierarchical_workflow - INFO - Processing with code_generator agent...
2025-05-31 13:56:05,971 - hierarchical_workflow - ERROR - Error in code_generator agent: BitNet simulation failed: 404
2025-05-31 13:56:05,971 - hierarchical_workflow - INFO - [bitnet_agent_complete] code_generator completed analysis...
2025-05-31 13:56:05,971 - hierarchical_workflow - INFO - Processing with context_summarizer agent...
2025-05-31 13:56:05,974 - hierarchical_workflow - ERROR - Error in context_summarizer agent: BitNet simulation failed: 404
2025-05-31 13:56:05,974 - hierarchical_workflow - INFO - [bitnet_agent_complete] context_summarizer completed analysis...
2025-05-31 13:56:05,974 - hierarchical_workflow - INFO - Processing with change_detector agent...
2025-05-31 13:56:05,976 - hierarchical_workflow - ERROR - Error in change_detector agent: BitNet simulation failed: 404
2025-05-31 13:56:05,976 - hierarchical_workflow - INFO - [bitnet_agent_complete] change_detector completed analysis...
2025-05-31 13:56:05,976 - hierarchical_workflow - INFO - [vllm_reasoning_start] Starting vLLM reasoning phase...
2025-05-31 13:56:05,977 - hierarchical_workflow - ERROR - Error in vLLM reasoning: vLLM reasoning failed: 404
2025-05-31 13:56:05,977 - hierarchical_workflow - INFO - [plan_generation_start] Generating plan.json...
2025-05-31 13:56:05,979 - hierarchical_workflow - ERROR - Error generating plan: Plan generation failed: 404
2025-05-31 13:56:05,979 - hierarchical_workflow - INFO - Conversation log saved to conversation_20250531_135605.json
2025-05-31 13:56:05,980 - hierarchical_workflow - INFO - [workflow_complete] Hierarchical workflow completed successfully...
2025-05-31 13:59:01,079 - hierarchical_workflow - INFO - Starting hierarchical workflow execution
2025-05-31 13:59:01,079 - hierarchical_workflow - INFO - [workflow_start] User request: Analyze this Python project...
2025-05-31 13:59:01,080 - hierarchical_workflow - INFO - [folder_analysis] Analyzing folder: test-project...
2025-05-31 13:59:01,080 - hierarchical_workflow - INFO - [folder_analysis_complete] Analyzed 3 files, ~19 tokens...
2025-05-31 13:59:01,080 - hierarchical_workflow - INFO - [bitnet_processing_start] Processing with 4 BitNet agents...
2025-05-31 13:59:01,080 - hierarchical_workflow - INFO - Processing with file_analyzer agent...
2025-05-31 13:59:01,086 - hierarchical_workflow - ERROR - Error in file_analyzer agent: All API endpoints failed. Last error: HTTP 404 from http://172.18.18.192:8001/v1/completions
2025-05-31 13:59:01,086 - hierarchical_workflow - INFO - [bitnet_agent_complete] file_analyzer completed analysis...
2025-05-31 13:59:01,086 - hierarchical_workflow - INFO - Processing with code_generator agent...
2025-05-31 13:59:01,091 - hierarchical_workflow - ERROR - Error in code_generator agent: All API endpoints failed. Last error: HTTP 404 from http://172.18.18.192:8001/v1/completions
2025-05-31 13:59:01,091 - hierarchical_workflow - INFO - [bitnet_agent_complete] code_generator completed analysis...
2025-05-31 13:59:01,091 - hierarchical_workflow - INFO - Processing with context_summarizer agent...
2025-05-31 13:59:01,095 - hierarchical_workflow - ERROR - Error in context_summarizer agent: All API endpoints failed. Last error: HTTP 404 from http://172.18.18.192:8001/v1/completions
2025-05-31 13:59:01,095 - hierarchical_workflow - INFO - [bitnet_agent_complete] context_summarizer completed analysis...
2025-05-31 13:59:01,095 - hierarchical_workflow - INFO - Processing with change_detector agent...
2025-05-31 13:59:01,100 - hierarchical_workflow - ERROR - Error in change_detector agent: All API endpoints failed. Last error: HTTP 404 from http://172.18.18.192:8001/v1/completions
2025-05-31 13:59:01,100 - hierarchical_workflow - INFO - [bitnet_agent_complete] change_detector completed analysis...
2025-05-31 13:59:01,100 - hierarchical_workflow - INFO - [vllm_reasoning_start] Starting vLLM reasoning phase...
2025-05-31 13:59:01,105 - hierarchical_workflow - ERROR - Error in vLLM reasoning: vLLM reasoning failed - all endpoints failed. Last error: HTTP 404 from http://172.18.18.192:8001/v1/completions
2025-05-31 13:59:01,105 - hierarchical_workflow - INFO - [plan_generation_start] Generating plan.json...
2025-05-31 13:59:01,107 - hierarchical_workflow - ERROR - Error generating plan: Plan generation failed: 404
2025-05-31 13:59:01,107 - hierarchical_workflow - INFO - Conversation log saved to conversation_20250531_135901.json
2025-05-31 13:59:01,107 - hierarchical_workflow - INFO - [workflow_complete] Hierarchical workflow completed successfully...
2025-05-31 14:02:12,641 - hierarchical_workflow - INFO - Starting hierarchical workflow execution
2025-05-31 14:02:12,642 - hierarchical_workflow - INFO - [workflow_start] User request: Analyze this Python project and suggest improvements...
2025-05-31 14:02:12,642 - hierarchical_workflow - INFO - [folder_analysis] Analyzing folder: test-project...
2025-05-31 14:02:12,642 - hierarchical_workflow - INFO - [folder_analysis_complete] Analyzed 3 files, ~19 tokens...
2025-05-31 14:02:12,642 - hierarchical_workflow - INFO - [bitnet_processing_start] Processing with 4 BitNet agents...
2025-05-31 14:02:12,642 - hierarchical_workflow - INFO - Processing with file_analyzer agent...
2025-05-31 14:02:12,656 - hierarchical_workflow - ERROR - Error in file_analyzer agent: All API endpoints failed. Last error: HTTP 400 from http://172.18.18.192:8001/v1/completions
2025-05-31 14:02:12,656 - hierarchical_workflow - INFO - [bitnet_agent_complete] file_analyzer completed analysis...
2025-05-31 14:02:12,656 - hierarchical_workflow - INFO - Processing with code_generator agent...
2025-05-31 14:02:12,661 - hierarchical_workflow - ERROR - Error in code_generator agent: All API endpoints failed. Last error: HTTP 400 from http://172.18.18.192:8001/v1/completions
2025-05-31 14:02:12,661 - hierarchical_workflow - INFO - [bitnet_agent_complete] code_generator completed analysis...
2025-05-31 14:02:12,661 - hierarchical_workflow - INFO - Processing with context_summarizer agent...
2025-05-31 14:02:12,666 - hierarchical_workflow - ERROR - Error in context_summarizer agent: All API endpoints failed. Last error: HTTP 400 from http://172.18.18.192:8001/v1/completions
2025-05-31 14:02:12,666 - hierarchical_workflow - INFO - [bitnet_agent_complete] context_summarizer completed analysis...
2025-05-31 14:02:12,666 - hierarchical_workflow - INFO - Processing with change_detector agent...
2025-05-31 14:02:12,674 - hierarchical_workflow - ERROR - Error in change_detector agent: All API endpoints failed. Last error: HTTP 400 from http://172.18.18.192:8001/v1/completions
2025-05-31 14:02:12,674 - hierarchical_workflow - INFO - [bitnet_agent_complete] change_detector completed analysis...
2025-05-31 14:02:12,674 - hierarchical_workflow - INFO - [vllm_reasoning_start] Starting vLLM reasoning phase...
2025-05-31 14:02:12,682 - hierarchical_workflow - ERROR - Error in vLLM reasoning: vLLM reasoning failed - all endpoints failed. Last error: HTTP 400 from http://172.18.18.192:8001/v1/completions
2025-05-31 14:02:12,682 - hierarchical_workflow - INFO - [plan_generation_start] Generating plan.json...
2025-05-31 14:02:12,684 - hierarchical_workflow - ERROR - Error generating plan: Plan generation failed: 404
2025-05-31 14:02:12,684 - hierarchical_workflow - INFO - Conversation log saved to conversation_20250531_140212.json
2025-05-31 14:02:12,684 - hierarchical_workflow - INFO - [workflow_complete] Hierarchical workflow completed successfully...
2025-05-31 14:08:47,347 - hierarchical_workflow - INFO - Starting hierarchical workflow execution
2025-05-31 14:08:47,347 - hierarchical_workflow - INFO - [workflow_start] User request: Analyze this Python project and suggest improvements...
2025-05-31 14:08:47,347 - hierarchical_workflow - INFO - [folder_analysis] Analyzing folder: test-project...
2025-05-31 14:08:47,348 - hierarchical_workflow - INFO - [folder_analysis_complete] Analyzed 3 files, ~19 tokens...
2025-05-31 14:08:47,348 - hierarchical_workflow - INFO - [bitnet_processing_start] Processing with 4 BitNet agents...
2025-05-31 14:08:47,348 - hierarchical_workflow - INFO - Processing with file_analyzer agent...
2025-05-31 14:08:47,355 - hierarchical_workflow - ERROR - Error in file_analyzer agent: API call failed: HTTP 400 from http://172.18.18.192:8001/v1/completions. Error: {"object":"error","message":"This model's maximum context length is 2048 tokens. However, you requested 3228 tokens (228 in the messages, 3000 in the completion). Please reduce the length of the messages or completion.","type":"BadRequestError","param":null,"code":400}
2025-05-31 14:08:47,355 - hierarchical_workflow - INFO - [bitnet_agent_complete] file_analyzer completed analysis...
2025-05-31 14:08:47,355 - hierarchical_workflow - INFO - Processing with code_generator agent...
2025-05-31 14:08:47,360 - hierarchical_workflow - ERROR - Error in code_generator agent: API call failed: HTTP 400 from http://172.18.18.192:8001/v1/completions. Error: {"object":"error","message":"This model's maximum context length is 2048 tokens. However, you requested 3221 tokens (221 in the messages, 3000 in the completion). Please reduce the length of the messages or completion.","type":"BadRequestError","param":null,"code":400}
2025-05-31 14:08:47,360 - hierarchical_workflow - INFO - [bitnet_agent_complete] code_generator completed analysis...
2025-05-31 14:08:47,361 - hierarchical_workflow - INFO - Processing with context_summarizer agent...
2025-05-31 14:08:47,366 - hierarchical_workflow - ERROR - Error in context_summarizer agent: API call failed: HTTP 400 from http://172.18.18.192:8001/v1/completions. Error: {"object":"error","message":"This model's maximum context length is 2048 tokens. However, you requested 3222 tokens (222 in the messages, 3000 in the completion). Please reduce the length of the messages or completion.","type":"BadRequestError","param":null,"code":400}
2025-05-31 14:08:47,366 - hierarchical_workflow - INFO - [bitnet_agent_complete] context_summarizer completed analysis...
2025-05-31 14:08:47,366 - hierarchical_workflow - INFO - Processing with change_detector agent...
2025-05-31 14:08:47,371 - hierarchical_workflow - ERROR - Error in change_detector agent: API call failed: HTTP 400 from http://172.18.18.192:8001/v1/completions. Error: {"object":"error","message":"This model's maximum context length is 2048 tokens. However, you requested 3223 tokens (223 in the messages, 3000 in the completion). Please reduce the length of the messages or completion.","type":"BadRequestError","param":null,"code":400}
2025-05-31 14:08:47,371 - hierarchical_workflow - INFO - [bitnet_agent_complete] change_detector completed analysis...
2025-05-31 14:08:47,371 - hierarchical_workflow - INFO - [vllm_reasoning_start] Starting vLLM reasoning phase...
2025-05-31 14:08:47,376 - hierarchical_workflow - ERROR - Error in vLLM reasoning: vLLM reasoning failed: HTTP 400 from http://172.18.18.192:8001/v1/completions. Error: {"object":"error","message":"This model's maximum context length is 2048 tokens. However, you requested 2201 tokens (201 in the messages, 2000 in the completion). Please reduce the length of the messages or completion.","type":"BadRequestError","param":null,"code":400}
2025-05-31 14:08:47,376 - hierarchical_workflow - INFO - [plan_generation_start] Generating plan.json...
2025-05-31 14:08:47,382 - hierarchical_workflow - ERROR - Error generating plan: Plan generation failed: HTTP 400 from http://172.18.18.192:8001/v1/completions. Error: {"object":"error","message":"This model's maximum context length is 2048 tokens. However, you requested 3735 tokens (735 in the messages, 3000 in the completion). Please reduce the length of the messages or completion.","type":"BadRequestError","param":null,"code":400}
2025-05-31 14:08:47,383 - hierarchical_workflow - INFO - Conversation log saved to conversation_20250531_140847.json
2025-05-31 14:08:47,383 - hierarchical_workflow - INFO - [workflow_complete] Hierarchical workflow completed successfully...
2025-05-31 14:11:24,353 - hierarchical_workflow - INFO - Starting hierarchical workflow execution
2025-05-31 14:11:24,354 - hierarchical_workflow - INFO - [workflow_start] User request: Analyze this Python project...
2025-05-31 14:11:24,354 - hierarchical_workflow - INFO - [folder_analysis] Analyzing folder: test-project...
2025-05-31 14:11:24,354 - hierarchical_workflow - INFO - [folder_analysis_complete] Analyzed 3 files, ~19 tokens...
2025-05-31 14:11:24,354 - hierarchical_workflow - INFO - [bitnet_processing_start] Processing with 4 BitNet agents...
2025-05-31 14:11:24,354 - hierarchical_workflow - INFO - Processing with file_analyzer agent...
2025-05-31 14:11:33,497 - hierarchical_workflow - INFO - [bitnet_agent_complete] file_analyzer completed analysis...
2025-05-31 14:11:33,497 - hierarchical_workflow - INFO - Processing with code_generator agent...
2025-05-31 14:11:41,867 - hierarchical_workflow - INFO - [bitnet_agent_complete] code_generator completed analysis...
2025-05-31 14:11:41,867 - hierarchical_workflow - INFO - Processing with context_summarizer agent...
2025-05-31 14:11:50,260 - hierarchical_workflow - INFO - [bitnet_agent_complete] context_summarizer completed analysis...
2025-05-31 14:11:50,260 - hierarchical_workflow - INFO - Processing with change_detector agent...
2025-05-31 14:12:00,373 - hierarchical_workflow - INFO - [bitnet_agent_complete] change_detector completed analysis...
2025-05-31 14:12:00,373 - hierarchical_workflow - INFO - [vllm_reasoning_start] Starting vLLM reasoning phase...
2025-05-31 14:12:18,285 - hierarchical_workflow - INFO - [vllm_reasoning_complete] vLLM reasoning completed...
2025-05-31 14:12:18,285 - hierarchical_workflow - INFO - [plan_generation_start] Generating plan.json...
2025-05-31 14:12:18,290 - hierarchical_workflow - ERROR - Error generating plan: Plan generation failed: HTTP 400 from http://172.18.18.192:8001/v1/completions. Error: {"object":"error","message":"This model's maximum context length is 2048 tokens. However, you requested 2362 tokens (2089 in the messages, 273 in the completion). Please reduce the length of the messages or completion.","type":"BadRequestError","param":null,"code":400}
2025-05-31 14:12:18,290 - hierarchical_workflow - INFO - Conversation log saved to conversation_20250531_141218.json
2025-05-31 14:12:18,290 - hierarchical_workflow - INFO - [workflow_complete] Hierarchical workflow completed successfully...
2025-05-31 14:13:27,344 - hierarchical_workflow - INFO - Starting hierarchical workflow execution
2025-05-31 14:13:27,344 - hierarchical_workflow - INFO - [workflow_start] User request: Analyze this project...
2025-05-31 14:13:27,344 - hierarchical_workflow - INFO - [folder_analysis] Analyzing folder: test-project...
2025-05-31 14:13:27,345 - hierarchical_workflow - INFO - [folder_analysis_complete] Analyzed 3 files, ~19 tokens...
2025-05-31 14:13:27,345 - hierarchical_workflow - INFO - [bitnet_processing_start] Processing with 4 BitNet agents...
2025-05-31 14:13:27,345 - hierarchical_workflow - INFO - Processing with file_analyzer agent...
2025-05-31 14:13:39,577 - hierarchical_workflow - INFO - [bitnet_agent_complete] file_analyzer completed analysis...
2025-05-31 14:13:39,577 - hierarchical_workflow - INFO - Processing with code_generator agent...
2025-05-31 14:13:49,706 - hierarchical_workflow - INFO - [bitnet_agent_complete] code_generator completed analysis...
2025-05-31 14:13:49,706 - hierarchical_workflow - INFO - Processing with context_summarizer agent...
2025-05-31 14:14:03,001 - hierarchical_workflow - INFO - [bitnet_agent_complete] context_summarizer completed analysis...
2025-05-31 14:14:03,001 - hierarchical_workflow - INFO - Processing with change_detector agent...
2025-05-31 14:14:15,617 - hierarchical_workflow - INFO - [bitnet_agent_complete] change_detector completed analysis...
2025-05-31 14:14:15,618 - hierarchical_workflow - INFO - [vllm_reasoning_start] Starting vLLM reasoning phase...
2025-05-31 14:14:33,182 - hierarchical_workflow - INFO - [vllm_reasoning_complete] vLLM reasoning completed...
2025-05-31 14:14:33,182 - hierarchical_workflow - INFO - [plan_generation_start] Generating plan.json...
2025-05-31 14:14:33,187 - hierarchical_workflow - ERROR - Error generating plan: Plan generation failed: HTTP 400 from http://172.18.18.192:8001/v1/completions. Error: {"object":"error","message":"This model's maximum context length is 2048 tokens. However, you requested 2217 tokens (1904 in the messages, 313 in the completion). Please reduce the length of the messages or completion.","type":"BadRequestError","param":null,"code":400}
2025-05-31 14:14:33,188 - hierarchical_workflow - INFO - Conversation log saved to conversation_20250531_141433.json
2025-05-31 14:14:33,188 - hierarchical_workflow - INFO - [workflow_complete] Hierarchical workflow completed successfully...
2025-05-31 14:16:10,236 - hierarchical_workflow - INFO - Starting hierarchical workflow execution
2025-05-31 14:16:10,236 - hierarchical_workflow - INFO - [workflow_start] User request: Simple analysis...
2025-05-31 14:16:10,236 - hierarchical_workflow - INFO - [folder_analysis] Analyzing folder: test-project...
2025-05-31 14:16:10,236 - hierarchical_workflow - INFO - [folder_analysis_complete] Analyzed 3 files, ~19 tokens...
2025-05-31 14:16:10,236 - hierarchical_workflow - INFO - [bitnet_processing_start] Processing with 4 BitNet agents...
2025-05-31 14:16:10,236 - hierarchical_workflow - INFO - Processing with file_analyzer agent...
2025-05-31 14:16:20,182 - hierarchical_workflow - INFO - [bitnet_agent_complete] file_analyzer completed analysis...
2025-05-31 14:16:20,182 - hierarchical_workflow - INFO - Processing with code_generator agent...
2025-05-31 14:16:30,086 - hierarchical_workflow - INFO - [bitnet_agent_complete] code_generator completed analysis...
2025-05-31 14:16:30,087 - hierarchical_workflow - INFO - Processing with context_summarizer agent...
2025-05-31 14:16:38,016 - hierarchical_workflow - INFO - [bitnet_agent_complete] context_summarizer completed analysis...
2025-05-31 14:16:38,017 - hierarchical_workflow - INFO - Processing with change_detector agent...
2025-05-31 14:16:42,949 - hierarchical_workflow - INFO - [bitnet_agent_complete] change_detector completed analysis...
2025-05-31 14:16:42,949 - hierarchical_workflow - INFO - [vllm_reasoning_start] Starting vLLM reasoning phase...
2025-05-31 14:17:02,249 - hierarchical_workflow - INFO - [vllm_reasoning_complete] vLLM reasoning completed...
2025-05-31 14:17:02,249 - hierarchical_workflow - INFO - [plan_generation_start] Generating plan.json...
2025-05-31 14:17:02,253 - hierarchical_workflow - ERROR - Error generating plan: Plan generation failed: HTTP 400 from http://172.18.18.192:8001/v1/completions. Error: {"object":"error","message":"This model's maximum context length is 2048 tokens. However, you requested 2299 tokens (2095 in the messages, 204 in the completion). Please reduce the length of the messages or completion.","type":"BadRequestError","param":null,"code":400}
2025-05-31 14:17:02,254 - hierarchical_workflow - INFO - Conversation log saved to conversation_20250531_141702.json
2025-05-31 14:17:02,254 - hierarchical_workflow - INFO - [workflow_complete] Hierarchical workflow completed successfully...
2025-05-31 14:17:57,576 - hierarchical_workflow - INFO - Starting hierarchical workflow execution
2025-05-31 14:17:57,576 - hierarchical_workflow - INFO - [workflow_start] User request: Quick test...
2025-05-31 14:17:57,576 - hierarchical_workflow - INFO - [folder_analysis] Analyzing folder: test-project...
2025-05-31 14:17:57,576 - hierarchical_workflow - INFO - [folder_analysis_complete] Analyzed 3 files, ~19 tokens...
2025-05-31 14:17:57,576 - hierarchical_workflow - INFO - [bitnet_processing_start] Processing with 4 BitNet agents...
2025-05-31 14:17:57,576 - hierarchical_workflow - INFO - Processing with file_analyzer agent...
2025-05-31 14:18:05,998 - hierarchical_workflow - INFO - [bitnet_agent_complete] file_analyzer completed analysis...
2025-05-31 14:18:05,999 - hierarchical_workflow - INFO - Processing with code_generator agent...
2025-05-31 14:18:16,737 - hierarchical_workflow - INFO - [bitnet_agent_complete] code_generator completed analysis...
2025-05-31 14:18:16,737 - hierarchical_workflow - INFO - Processing with context_summarizer agent...
2025-05-31 14:18:26,143 - hierarchical_workflow - INFO - [bitnet_agent_complete] context_summarizer completed analysis...
2025-05-31 14:18:26,143 - hierarchical_workflow - INFO - Processing with change_detector agent...
2025-05-31 14:18:32,225 - hierarchical_workflow - INFO - [bitnet_agent_complete] change_detector completed analysis...
2025-05-31 14:18:32,226 - hierarchical_workflow - INFO - [vllm_reasoning_start] Starting vLLM reasoning phase...
2025-05-31 14:18:51,308 - hierarchical_workflow - INFO - [vllm_reasoning_complete] vLLM reasoning completed...
2025-05-31 14:18:51,309 - hierarchical_workflow - INFO - [plan_generation_start] Generating plan.json...
2025-05-31 14:18:51,313 - hierarchical_workflow - ERROR - Error generating plan: Plan generation failed: HTTP 400 from http://172.18.18.192:8001/v1/completions. Error: {"object":"error","message":"This model's maximum context length is 2048 tokens. However, you requested 2381 tokens (2104 in the messages, 277 in the completion). Please reduce the length of the messages or completion.","type":"BadRequestError","param":null,"code":400}
2025-05-31 14:18:51,314 - hierarchical_workflow - INFO - Conversation log saved to conversation_20250531_141851.json
2025-05-31 14:18:51,314 - hierarchical_workflow - INFO - [workflow_complete] Hierarchical workflow completed successfully...
2025-05-31 14:19:50,478 - hierarchical_workflow - INFO - Starting hierarchical workflow execution
2025-05-31 14:19:50,479 - hierarchical_workflow - INFO - [workflow_start] User request: Test analysis...
2025-05-31 14:19:50,479 - hierarchical_workflow - INFO - [folder_analysis] Analyzing folder: test-project...
2025-05-31 14:19:50,479 - hierarchical_workflow - INFO - [folder_analysis_complete] Analyzed 3 files, ~19 tokens...
2025-05-31 14:19:50,479 - hierarchical_workflow - INFO - [bitnet_processing_start] Processing with 4 BitNet agents...
2025-05-31 14:19:50,479 - hierarchical_workflow - INFO - Processing with file_analyzer agent...
2025-05-31 14:20:03,200 - hierarchical_workflow - INFO - [bitnet_agent_complete] file_analyzer completed analysis...
2025-05-31 14:20:03,200 - hierarchical_workflow - INFO - Processing with code_generator agent...
2025-05-31 14:20:12,066 - hierarchical_workflow - INFO - [bitnet_agent_complete] code_generator completed analysis...
2025-05-31 14:20:12,066 - hierarchical_workflow - INFO - Processing with context_summarizer agent...
2025-05-31 14:20:20,116 - hierarchical_workflow - INFO - [bitnet_agent_complete] context_summarizer completed analysis...
2025-05-31 14:20:20,116 - hierarchical_workflow - INFO - Processing with change_detector agent...
2025-05-31 14:20:33,731 - hierarchical_workflow - INFO - [bitnet_agent_complete] change_detector completed analysis...
2025-05-31 14:20:33,731 - hierarchical_workflow - INFO - [vllm_reasoning_start] Starting vLLM reasoning phase...
2025-05-31 14:20:53,032 - hierarchical_workflow - INFO - [vllm_reasoning_complete] vLLM reasoning completed...
2025-05-31 14:20:53,032 - hierarchical_workflow - INFO - [plan_generation_start] Generating plan.json...
2025-05-31 14:21:03,031 - hierarchical_workflow - ERROR - Failed to parse plan JSON: Expecting value: line 1 column 1 (char 0)
2025-05-31 14:21:03,032 - hierarchical_workflow - INFO - Conversation log saved to conversation_20250531_142103.json
2025-05-31 14:21:03,032 - hierarchical_workflow - INFO - [workflow_complete] Hierarchical workflow completed successfully...
