2025-05-31 13:10:35,985 - bitnet_system - INFO - Configuration validated successfully
2025-05-31 13:10:35,985 - bitnet_system - INFO - BitNet Multi-Agent System initialized
2025-05-31 13:10:35,985 - bitnet_system - INFO - Starting BitNet Multi-Agent System...
2025-05-31 13:10:35,985 - bitnet_system - INFO - Starting local deployment...
2025-05-31 13:10:35,986 - bitnet_system - INFO - <PERSON>is is already running
2025-05-31 13:10:35,986 - bitnet_system - INFO - Started BitNet agent 1 (analyzer) on port 9001
2025-05-31 13:10:36,988 - bitnet_system - INFO - Started BitNet agent 2 (coder) on port 9002
2025-05-31 13:10:37,990 - bitnet_system - INFO - Started BitNet agent 3 (reviewer) on port 9003
2025-05-31 13:10:38,992 - bitnet_system - INFO - Started BitNet agent 4 (researcher) on port 9004
2025-05-31 13:10:39,994 - bitnet_system - INFO - Local deployment started
2025-05-31 13:10:40,211 - openai._base_client - INFO - Retrying request to /completions in 0.451551 seconds
2025-05-31 13:10:40,664 - openai._base_client - INFO - Retrying request to /completions in 0.853623 seconds
2025-05-31 13:10:41,518 - reasoning_agent - ERROR - Failed to initialize vLLM model: Connection error.
2025-05-31 13:10:41,519 - reasoning_agent - INFO - Reasoning Agent initialized
2025-05-31 13:10:41,519 - orchestrator - INFO - Multi-Agent Orchestrator initialized
2025-05-31 13:10:41,520 - orchestrator - INFO - Redis connection established
2025-05-31 13:10:41,520 - communication_manager - INFO - Communication manager initialized
2025-05-31 13:10:41,520 - orchestrator - WARNING - No BitNet agents discovered
2025-05-31 13:10:41,520 - orchestrator - INFO - Orchestrator initialization completed
2025-05-31 13:10:41,520 - bitnet_system - INFO - Orchestrator started successfully
2025-05-31 13:10:41,520 - bitnet_system - INFO - System started successfully!
2025-05-31 13:10:41,520 - bitnet_system - INFO - ============================================================
2025-05-31 13:10:41,520 - bitnet_system - INFO - SYSTEM STATUS
2025-05-31 13:10:41,520 - bitnet_system - INFO - ============================================================
2025-05-31 13:10:41,520 - bitnet_system - INFO - Overall Status: degraded
2025-05-31 13:10:41,520 - bitnet_system - INFO - Active Agents: 0
2025-05-31 13:10:41,520 - bitnet_system - INFO - Total Tasks: 0
2025-05-31 13:10:41,520 - bitnet_system - INFO - Reasoning Agent: unhealthy
2025-05-31 13:10:41,520 - bitnet_system - INFO - ============================================================
2025-05-31 13:10:41,520 - bitnet_system - INFO - Starting interactive mode...
2025-05-31 13:10:41,520 - bitnet_system - INFO - Type 'help' for available commands, 'quit' to exit
2025-05-31 13:11:05,825 - bitnet_system - INFO - Stopping BitNet Multi-Agent System...
2025-05-31 13:11:05,825 - communication_manager - INFO - Communication manager cleanup completed
2025-05-31 13:11:05,825 - orchestrator - INFO - Orchestrator cleanup completed
2025-05-31 13:11:05,826 - bitnet_system - INFO - Stopped process: agent_1
2025-05-31 13:11:05,826 - bitnet_system - INFO - Stopped process: agent_2
2025-05-31 13:11:05,826 - bitnet_system - INFO - Stopped process: agent_3
2025-05-31 13:11:05,826 - bitnet_system - INFO - Stopped process: agent_4
2025-05-31 13:11:05,958 - bitnet_system - INFO - Docker containers stopped
2025-05-31 13:11:05,958 - bitnet_system - INFO - System stopped successfully
2025-05-31 13:11:05,958 - communication_manager - INFO - Message processor cancelled
2025-05-31 13:11:05,958 - communication_manager - INFO - Message cleanup cancelled
